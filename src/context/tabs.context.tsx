/*
 * Copyright 2024 (c) Jaguar Land Rover Ltd. All rights reserved.
 */

import { createContext, ReactNode, useMemo, useState } from "react";

interface TabsContextI {
  showStatus: boolean;
  activeTab: string;
  setShowStatus: (value: boolean) => void;
  setActiveTab: (value: string) => void;
}

export const TabsContext = createContext<TabsContextI>({
  showStatus: false,
  activeTab: "search",
  setShowStatus: () => undefined,
  setActiveTab: () => undefined,
});

export const TabsContextProvider = ({ children }: { children: ReactNode }) => {
  const [showStatus, setShowStatus] = useState(false);
  const [activeTab, setActiveTab] = useState("search");

  const value = useMemo(
    () => ({
      showStatus,
      activeTab,
      setShowStatus,
      setActiveTab,
    }),
    [showStatus, activeTab]
  );

  return <TabsContext.Provider value={value}>{children}</TabsContext.Provider>;
};
