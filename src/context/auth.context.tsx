/*
 * Copyright (c) Jaguar Land Rover Ltd 2022. All rights reserved
 */

import { createContext, ReactNode, useEffect, useMemo, useRef, useState } from "react";
import { initKc, kc } from "services/keycloak.service";
import { useIdle } from "hooks/use-idle.hook";
import { Role } from "types/permissions";

const idleTimeout = 1000 * 60 * 15; // 15 min
const refreshInterval = 1000 * 60 * 29; // 29 min

export interface AuthValues {
  firstName: string;
  lastName: string;
  roles: Role[];
  loginStatus: "checking" | "loggedin" | "error";
}

const defaultValues: AuthValues = {
  firstName: "",
  lastName: "",
  roles: [],
  loginStatus: "checking",
};

export const AuthContext = createContext(defaultValues);

export const AuthContextProvider = ({ children }: { children: ReactNode }) => {
  const kcInitialised = useRef(false);
  const [state, setState] = useState(defaultValues);
  // If user is idle for more than 15 min, force logout.
  useIdle(idleTimeout, kc.logout);

  useEffect(() => {
    // Prevents useEffect callback from running more than once
    if (kcInitialised.current) return;
    let intervalId: number;

    initKc()
      .then((authenticated) => {
        if (!authenticated || !kc.tokenParsed) return kc.login();

        setState((newState) => ({
          ...newState,
          firstName: kc.tokenParsed?.given_name,
          lastName: kc.tokenParsed?.family_name,
          roles: kc.tokenParsed?.authorities,
          loginStatus: "loggedin",
        }));

        // Refresh KC token every 29 minutes. Keycloak tokens expire after 30 minutes.
        intervalId = window.setInterval(() => {
          kc.updateToken(1000 * 60 * 5);
        }, refreshInterval);
      })
      .catch((err) => {
        console.log("Auth failed:", err);
        setState((newState) => ({ ...newState, loginStatus: "error" }));
      });

    kcInitialised.current = true;
    return () => clearInterval(intervalId);
  }, []);

  const value = useMemo(() => state, [state]);

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
