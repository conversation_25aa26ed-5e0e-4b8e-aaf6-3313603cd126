/*
 * Copyright 2022 (c) Jaguar Land Rover Ltd. All rights reserved.
 */

import { ReactNode, useMemo } from "react";
import { MutationCache, QueryCache, QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { useNotification } from "hooks/use-notification.hook";

const notifyError = {
  type: "error",
  title: "Unexpected error",
  body: "Something went wrong. If the problem persists, please contact IT.",
} as const;

const defaultOptions = {
  queries: { retry: 1 },
};

export const ReactQueryContextProvider = ({ children }: { children: ReactNode }) => {
  const { notify } = useNotification();

  const queryClient = useMemo(() => {
    const onError = (error: unknown) => {
      notify(notifyError);
      console.log(error);
    };

    const queryCache = new QueryCache({ onError });
    const mutationCache = new MutationCache({ onError });

    return new QueryClient({ defaultOptions, queryCache, mutationCache });
  }, [notify]);

  return <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>;
};
