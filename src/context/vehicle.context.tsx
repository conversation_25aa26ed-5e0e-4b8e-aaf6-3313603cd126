/*
 * Copyright 2023 (c) Jaguar Land Rover Ltd. All rights reserved.
 */

import { createContext, ReactNode, useMemo, useState } from "react";
import { Vehicle } from "types/data";

interface VehicleContextI {
  vehicles: Vehicle[];
  setVehicles: (value: Vehicle[]) => void;
}

export const VehicleContext = createContext<VehicleContextI>({
  vehicles: [] as Vehicle[],
  setVehicles: () => undefined,
});

export const VehicleContextProvider = ({ children }: { children: ReactNode }) => {
  const [vehicles, setVehicles] = useState([] as Vehicle[]);

  const value = useMemo(
    () => ({
      vehicles,
      setVehicles,
    }),
    [vehicles]
  );

  return <VehicleContext.Provider value={value}>{children}</VehicleContext.Provider>;
};
