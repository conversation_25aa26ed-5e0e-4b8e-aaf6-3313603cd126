/*
 * Copyright 2024 (c) Jaguar Land Rover Ltd. All rights reserved.
 */

import { createContext, ReactNode, useMemo, useState } from "react";

interface FleetContextI {
  vinsAlreadyExist: string[];
  invalidVins: string[];
  duplicateVins: string[];
  vinsToAdd: string[];
  vinsToRemove: string[];
  setVinsAlreadyExist: (value: string[]) => void;
  setInvalidVins: (value: string[]) => void;
  setDuplicateVins: (value: string[]) => void;
  setVinsToAdd: (value: string[]) => void;
  setVinsToRemove: (value: string[]) => void;
}

export const FleetContext = createContext<FleetContextI>({
  vinsAlreadyExist: [],
  invalidVins: [],
  duplicateVins: [],
  vinsToAdd: [],
  vinsToRemove: [],
  setVinsAlreadyExist: () => undefined,
  setInvalidVins: () => undefined,
  setDuplicateVins: () => undefined,
  setVinsToAdd: () => undefined,
  setVinsToRemove: () => undefined,
});

export const FleetContextProvider = ({ children }: { children: ReactNode }) => {
  const [vinsAlreadyExist, setVinsAlreadyExist] = useState<string[]>([]);
  const [invalidVins, setInvalidVins] = useState<string[]>([]);
  const [duplicateVins, setDuplicateVins] = useState<string[]>([]);
  const [vinsToAdd, setVinsToAdd] = useState<string[]>([]);
  const [vinsToRemove, setVinsToRemove] = useState<string[]>([]);

  const value = useMemo(
    () => ({
      vinsAlreadyExist,
      setVinsAlreadyExist,
      invalidVins,
      setInvalidVins,
      duplicateVins,
      setDuplicateVins,
      vinsToAdd,
      setVinsToAdd,
      vinsToRemove,
      setVinsToRemove,
    }),
    [
      vinsAlreadyExist,
      setVinsAlreadyExist,
      invalidVins,
      setInvalidVins,
      duplicateVins,
      setDuplicateVins,
      vinsToAdd,
      setVinsToAdd,
      vinsToRemove,
      setVinsToRemove,
    ]
  );

  return <FleetContext.Provider value={value}>{children}</FleetContext.Provider>;
};
