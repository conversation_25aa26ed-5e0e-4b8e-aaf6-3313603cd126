/*
 * Copyright 2022 (c) Jaguar Land Rover Ltd. All rights reserved.
 */

import Keycloak from "keycloak-js";
import { ENV_CONTEXT, getKeycloakConfig } from "utils/constants";

const keycloakConfig = getKeycloakConfig();

export const kc = new Keycloak({
  clientId: keycloakConfig.clientId,
  realm: keycloakConfig.realm,
  url: ENV_CONTEXT.keycloak_uri,
});

export const initKc = () => {
  return kc.init({
    onLoad: "check-sso",
    pkceMethod: "S256",
    silentCheckSsoRedirectUri: window.location.origin + "/silent-check-sso.html",
  });
};
