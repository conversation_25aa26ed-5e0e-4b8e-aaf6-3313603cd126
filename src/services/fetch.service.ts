import { kc } from "./keycloak.service";

interface Result<T> {
  data: T;
  status: number;
}

type Get = <T>(url: string | URL, customOptions?: RequestInit) => Promise<Result<T>>;

type Post = <T>(
  url: string | URL,
  payload: unknown,
  stringify?: boolean,
  customOptions?: RequestInit
) => Promise<Result<T>>;

type Delete = <T>(
  url: string | URL,
  payload?: unknown,
  customOptions?: RequestInit
) => Promise<Result<T>>;

const defaultOptions = () => {
  if (!kc.token) throw new Error("Keycloak Token not set");
  return {
    headers: {
      accept: "*/*",
      Authorization: `Bearer ${kc.token}`,
      "Content-Type": "application/json; charset=utf-8",
    },
  };
};

const modifiedDefaultOptions = (customOptions: RequestInit = {}) => {
  return {
    ...defaultOptions(),
    ...customOptions,
    headers: {
      ...defaultOptions().headers,
      ...customOptions.headers,
    },
  };
};

const fetchResult = async (response: Response) => {
  const { status, statusText } = response;
  if (!response.ok) throw new Error(statusText);
  try {
    const data = await response.json();
    return { data, status };
  } catch {
    return { data: {}, status };
  }
};

export const get: Get = async (url, customOptions = {}) => {
  const options = modifiedDefaultOptions(customOptions);
  const response = await fetch(url, options);
  return fetchResult(response);
};

export const post: Post = async (url, payload, stringify = true, customOptions = {}) => {
  const body = stringify ? JSON.stringify(payload) : payload;
  const options: RequestInit = {
    body: body as BodyInit,
    method: "POST",
    ...modifiedDefaultOptions(customOptions),
  };
  const response = await fetch(url, options);
  return fetchResult(response);
};

export const put: Post = async (url, payload, stringify = true, customOptions = {}) => {
  const body = stringify ? JSON.stringify(payload) : payload;
  const options: RequestInit = {
    body: body as BodyInit,
    method: "PUT",
    ...modifiedDefaultOptions(customOptions),
  };
  const response = await fetch(url, options);
  return fetchResult(response);
};

export const patch: Post = async (url, payload, stringify = true, customOptions = {}) => {
  const body = stringify ? JSON.stringify(payload) : payload;
  const options: RequestInit = {
    body: body as BodyInit,
    method: "PATCH",
    ...modifiedDefaultOptions(customOptions),
  };
  const response = await fetch(url, options);
  return fetchResult(response);
};

export const del: Delete = async (url, payload = {}, customOptions = {}) => {
  const options = {
    body: JSON.stringify(payload),
    method: "DELETE",
    ...modifiedDefaultOptions(customOptions),
  };
  const response = await fetch(url, options);
  return fetchResult(response);
};
