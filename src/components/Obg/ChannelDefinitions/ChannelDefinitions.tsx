/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

import { useQuery } from "@tanstack/react-query";
import { useState } from "react";
import { getChannelDefinitions } from "api/obg-discovery.api";
import { Pagination } from "components/Pagination";
import { SearchFilter } from "components/SearchFilter";
import { Spinner } from "components/Spinner";
import { useModalHandler } from "hooks/use-modal-handler.hook";
import { usePagination } from "hooks/use-pagination.hook";
import { useSearchFilter } from "hooks/use-search-filter.hook";
import { ChannelDefinition } from "types/obg-discovery";
import { getDateTime, sortByCreatedDate } from "utils/dates";
import { Card } from "components/Card";
import { randomID } from "utils/random";
import { Tooltip } from "components/Overlays";
import { ChannelModal, ChannelModalContent } from "./ChannelModal";

export const ChannelDefinitions = () => {
  const { useModal } = useModalHandler("channelModal");
  const [modalContent, setModalContent] = useState<ChannelModalContent>({
    title: "",
    channels: [],
  });

  const {
    isLoading: isLoadingDefinitions,
    isSuccess,
    data: channelDefinitions,
  } = useQuery({
    queryKey: ["getChannelDefinitions"],
    queryFn: () => getChannelDefinitions(),
  });

  const { searchFilter, filteredItems } = useSearchFilter<ChannelDefinition>(
    isSuccess ? [...channelDefinitions.data].sort(sortByCreatedDate) : [],
    "application"
  );

  const { data, handlePageChange, page, rowsPerPage, setPage, setRowsPerPage, total } =
    usePagination<ChannelDefinition>(filteredItems);

  const displayTableBody = () => {
    if (isLoadingDefinitions) {
      return (
        <tr className="text-center">
          <td colSpan={8}>
            <Spinner />
          </td>
        </tr>
      );
    }

    return data.map((definitions: ChannelDefinition) => (
      <tr key={randomID()}>
        <td>{definitions.application}</td>
        <td>{definitions.createdBy || <span className="na">N/A</span>}</td>
        <td>
          {definitions.createdDate ? (
            getDateTime(definitions.createdDate)
          ) : (
            <span className="na">N/A</span>
          )}
        </td>
        <td>{definitions.updatedBy || <span className="na">N/A</span>}</td>
        <td>
          {definitions.updatedDate ? (
            getDateTime(definitions.updatedDate)
          ) : (
            <span className="na">N/A</span>
          )}
        </td>
        <td>
          {definitions.permission ? (
            displayPermissions(definitions.permission)
          ) : (
            <span className="na">N/A</span>
          )}
        </td>
        <td>{definitions.blacklist ? "True" : "False"}</td>
        <td>
          <button
            className="btn btn-dark"
            onClick={() => {
              setModalContent({ title: definitions.application, channels: definitions.channels });
              useModal?.show();
            }}
            type="button"
          >
            Inspect
          </button>
        </td>
      </tr>
    ));
  };

  const displayPermissions = (permissions: string[]) => {
    if (permissions.length > 1) {
      return (
        <Tooltip
          label={permissions.map((permission) => (
            <li key={randomID()}>{permission}</li>
          ))}
        >
          {permissions[0]} <span className="channelPermissions">+{permissions.length - 1}</span>
        </Tooltip>
      );
    }
    return permissions[0];
  };

  return (
    <>
      <Card
        bodyStyles="text-center p-0 scroll-list"
        cardStyles="border-0"
        title="Channel Definitions"
      >
        <ChannelModal modalContent={modalContent} />
        <table aria-label="channelTable" className="table table-bordered table-heading text-center">
          <thead>
            <tr className="align-top">
              <th>Application</th>
              <th>Created By</th>
              <th>Created Date</th>
              <th>Updated By</th>
              <th>Updated Date</th>
              <th>Permission</th>
              <th>Blacklist</th>
              <th>Channels</th>
            </tr>
          </thead>
          <tbody className="align-middle">{displayTableBody()}</tbody>
        </table>
      </Card>

      <SearchFilter searchFilter={searchFilter} />

      <Pagination
        handlePageChange={handlePageChange}
        page={page}
        rowsPerPage={rowsPerPage}
        setPage={setPage}
        setRowsPerPage={setRowsPerPage}
        total={total}
      />
    </>
  );
};
