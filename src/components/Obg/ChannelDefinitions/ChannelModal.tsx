/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

import { useState } from "react";
import J<PERSON><PERSON><PERSON><PERSON> from "react-json-pretty";
import { Channel } from "types/obg-discovery";
import { randomID } from "utils/random";
import styles from "./ChannelModal.module.scss";

interface ChannelModalProps {
  modalContent: ChannelModalContent;
}

export interface ChannelModalContent {
  title: string;
  channels: Channel[];
}

export function ChannelModal(props: ChannelModalProps) {
  const [jsonView, setJsonView] = useState(false);

  const { modalContent } = props;
  return (
    <div
      aria-hidden="true"
      aria-labelledby="channelModalLabel"
      className="modal fade"
      id="channelModal"
      tabIndex={-1}
    >
      <div className={`modal-dialog modal-lg ${styles.channelModal}`} role="document">
        <div className="modal-content">
          <div className="modal-header">
            <h5 className="modal-title" id="contained-modal-title-vcenter">
              <b>Application:</b> {modalContent.title}
            </h5>
            <button
              aria-label="Close"
              className="btn-close"
              data-bs-dismiss="modal"
              onClick={() => setJsonView(false)}
              role="button"
            />
          </div>
          <div className={styles.switchButton}>
            <button
              className="btn btn-dark"
              onClick={() => {
                setJsonView(!jsonView);
              }}
              type="button"
            >
              Switch to {jsonView ? "Table" : "JSON"}
            </button>
          </div>

          {!jsonView && (
            <div className="modal-body">
              <table aria-label="channelTable" className="table table-bordered table-heading">
                <thead>
                  <tr>
                    <th>ID</th>
                    <th>Protocol</th>
                    <th>Endpoint</th>
                    <th>Priority</th>
                    <th>Method</th>
                  </tr>
                </thead>
                <tbody className="align-middle">
                  {modalContent.channels.map((channel: Channel) => (
                    <tr key={randomID()} style={{ lineHeight: "2.5" }}>
                      <td>{channel.id}</td>
                      <td>{channel.protocol}</td>
                      <td>{channel.endpoint}</td>
                      <td>{channel.priority}</td>
                      <td>{channel.method}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
          {jsonView && (
            <div className={`modal-body ${styles.jsonBody}`}>
              <JSONPretty data={modalContent} id="json-pretty" />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
