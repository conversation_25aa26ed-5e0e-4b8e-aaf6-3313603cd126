/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

import { useMutation } from "@tanstack/react-query";
import { Card } from "components/Card";
import { useNotification } from "hooks/use-notification.hook";
import { createProtocol, ProtocolFormI } from "api/obg.api";
import { useForm } from "hooks/use-form";
import { TextArea, TextInput } from "components/Form";
import { ErrorCircleIcon, TickCircle } from "components/Icons";
import { Spinner } from "components/Spinner";

interface CreateProtocolI {
  resetProtocol: () => void;
}

const validations = {
  vehicleId: { required: true, min: 2, max: 100 },
  protocolPayload: { required: true, min: 3, max: 10000 },
};

export const CreateProtocol = ({ resetProtocol }: CreateProtocolI) => {
  const { notify } = useNotification();

  const { formValues, errors, onChange, onSubmit } = useForm<ProtocolFormI>({
    defaultValues: { vehicleId: "", protocolPayload: "" },
    validations,
  });

  const mutate = useMutation({
    mutationFn: createProtocol,
    onSuccess: () =>
      notify({
        type: "success",
        title: "Protocol created successfully",
      }),
    onError: () =>
      notify({
        type: "error",
        title: "Protocol creation error",
      }),
  });

  const handleSubmitForm = async (submitValues: ProtocolFormI) => {
    if (submitValues && Object.values(submitValues).some((value) => !value)) {
      notify({
        type: "warning",
        title: "All required fields have not been entered",
      });
      return;
    }

    try {
      mutate.mutate(submitValues);
    } catch (error) {
      if (error instanceof Error) {
        if (error.name === "SyntaxError") {
          notify({
            type: "error",
            title: `JSON entered in file is not valid: ${submitValues.protocolPayload}`,
          });
        } else {
          notify({
            type: "error",
            title: "500 - Error Creating Configuration",
          });
        }
      }
    }
  };

  const displayResult = () => {
    if (mutate.isPending) {
      return <Spinner />;
    }

    if (mutate.error) {
      return (
        <div className="d-flex flex-column gap-2">
          <div className="d-flex flex-row gap-2 align-items-center">
            <ErrorCircleIcon />
            <p className="m-0">Something went wrong</p>
          </div>
          <div>
            <button
              className="btn btn-outline-dark my-2 noWrap"
              onClick={resetProtocol}
              role="button"
            >
              Try again
            </button>
          </div>
        </div>
      );
    }

    if (mutate.isSuccess) {
      return (
        <div className="d-flex flex-column gap-2">
          <div className="d-flex flex-row gap-2 align-items-center">
            <TickCircle />
            <p className="m-0">Payload sent successfully</p>
          </div>
          <div>
            <button
              className="btn btn-outline-dark my-2 noWrap"
              onClick={resetProtocol}
              role="button"
            >
              Send another
            </button>
          </div>
        </div>
      );
    }

    return (
      <>
        <TextInput
          error={errors.vehicleId}
          isRequired
          label="Enter a Vehicle Id"
          onChange={onChange("vehicleId")}
          placeholder="Type here..."
          value={formValues.vehicleId}
        />

        <TextArea
          error={errors.protocolPayload}
          isRequired
          label="Enter a valid JSON Object"
          onChange={onChange("protocolPayload")}
          placeholder="Paste or type here..."
          value={formValues.protocolPayload}
        />

        <div>
          <button className="btn btn-dark my-2 noWrap" type="submit">
            Submit
          </button>
        </div>
      </>
    );
  };

  return (
    <Card title="Create a new OBG payload">
      <form className="d-flex flex-column gap-3" onSubmit={onSubmit(handleSubmitForm)}>
        {displayResult()}
      </form>
    </Card>
  );
};
