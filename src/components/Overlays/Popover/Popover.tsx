/*
 * Copyright 2023 (c) Jaguar Land Rover Ltd. All rights reserved.
 */

import { ReactNode, useEffect, useRef, useState } from "react";
import { autoUpdate, flip, offset, shift, useFloating } from "@floating-ui/react-dom";
import { createPortal } from "react-dom";
import { randomID } from "utils/random";
import { ClickIcon } from "components/Icons";
import styles from "./Popover.module.scss";

interface PopoverProps {
  label: string | ReactNode;
  clickIcon?: boolean;
  children: ReactNode;
}

export const Popover = ({ label, children, clickIcon = true }: PopoverProps) => {
  const [ID] = useState(randomID);
  const [isOpen, setIsOpen] = useState(false);
  const popover = useRef<HTMLDivElement>(null);
  const { x, y, refs, strategy } = useFloating({
    whileElementsMounted: autoUpdate,
    middleware: [offset(5), flip(), shift()]
  });

  useEffect(() => {
    const onClick = (ev: MouseEvent): void => {
      const clickedElement = ev.target as HTMLElement;
      const _label = refs.floating.current;
      if (isOpen && !_label?.contains(clickedElement)) {
        setIsOpen(false);
      }
    };

    const onKeyDown = ({ code }: globalThis.KeyboardEvent) => {
      if (code === "Escape") setIsOpen(false);
    };

    document.addEventListener("click", onClick);
    document.addEventListener("keydown", onKeyDown);

    return () => {
      document.removeEventListener("click", onClick);
      document.removeEventListener("keydown", onKeyDown);
    };
  }, [isOpen, refs.floating]);

  const labelDiv = (
    <div
      className={styles.popover}
      id={ID}
      ref={refs.setFloating}
      role="dialog"
      style={{ position: strategy, top: y ?? 0, left: x ?? 0, zIndex: 101 }}
    >
      {label}
    </div>
  );

  return (
    <div ref={popover}>
      {createPortal(isOpen && labelDiv, document.body)}
      <button
        aria-describedby={ID}
        className={styles.container}
        onClick={(ev) => {
          ev.stopPropagation();
          setIsOpen(!isOpen);
        }}
        ref={refs.setReference}
      >
        {children}
        {clickIcon && <ClickIcon />}
      </button>
    </div>
  );
};
