/*
 * Copyright 2023 (c) Jaguar Land Rover Ltd. All rights reserved.
 */

import { ReactNode, useEffect, useState } from "react";
import { createPortal } from "react-dom";
import { autoUpdate, flip, offset, shift, useFloating } from "@floating-ui/react-dom";
import { randomID } from "utils/random";
import styles from "./Tooltip.module.scss";

interface TooltipProps {
  label: string | ReactNode;
  children: ReactNode;
}

export const Tooltip = ({ label, children }: TooltipProps) => {
  const [ID] = useState(randomID);
  const [hovering, setHovering] = useState(false);
  const { x, y, refs, strategy } = useFloating({
    whileElementsMounted: autoUpdate,
    middleware: [offset(5), flip(), shift()]
  });

  useEffect(() => {
    const onKeyDown = ({ code }: globalThis.KeyboardEvent) => {
      if (code === "Escape") setHovering(false);
    };
    document.addEventListener("keydown", onKeyDown);
    return () => document.removeEventListener("keydown", onKeyDown);
  }, []);

  const labelDiv = (
    <div
      className={styles.tooltip}
      id={ID}
      ref={refs.setFloating}
      role="tooltip"
      style={{ position: strategy, top: y ?? 0, left: x ?? 0, zIndex: 101 }}
    >
      {label}
    </div>
  );

  return (
    <>
      {createPortal(hovering && labelDiv, document.body)}
      <button
        aria-describedby={ID}
        className={styles.container}
        onBlur={() => setHovering(false)}
        onFocus={() => setHovering(true)}
        onMouseEnter={() => setHovering(true)}
        onMouseLeave={() => setHovering(false)}
        ref={refs.setReference}
      >
        {children}
      </button>
    </>
  );
};
