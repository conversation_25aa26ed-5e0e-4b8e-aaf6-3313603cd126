/*
 * Copyright 2023 (c) Jaguar Land Rover Ltd. All rights reserved.
 */

@use "sass:color";
@use "src/styles/variables" as *;
@use "src/styles/animations" as *;

.container {
  position: fixed;
  top: 0.5rem;
  right: 0.5rem;
  z-index: 1000;
  display: flex;
  flex-direction: column-reverse;
  gap: 0.5rem;
  transition: all 1s;
  .notification {
    @include slide-in-animation(100px, $direction: "horizontal", $duration: 0.5s);
    width: 350px;
    transition: width 0.3s ease;
    padding: 0.5rem;
    background: $white;
    position: relative;
    box-shadow: -1px 1px 3px rgba(0, 0, 0, 0.25);
    & > div {
      padding: 0.5rem;
      border-left: solid 4px $primary;
      .title {
        display: flex;
        gap: 0.2rem;
        align-items: center;
      }
    }
    &.error {
      & > div {
        border-left-color: $red;
        .title {
          color: $red;
        }
      }
    }
    &.success {
      & > div {
        border-left-color: $green;
        .title {
          color: $green;
        }
      }
    }
    &.warning {
      & > div {
        border-left-color: $amber;
        .title {
          color: $amber;
        }
      }
    }

    .errorList {
      margin-bottom: 0.7rem;
    }

    .close {
      border: none;
      cursor: pointer;
      opacity: 0.6;
      transition: opacity 0.2s;
      position: absolute;
      right: 0.5rem;
      top: 0.5rem;
      &:hover {
        opacity: 0.9;
      }
    }
  }

  .expanded {
    width: 450px;
  }

  .errorContent {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.4s ease;
  }

  .errorContentExpanded {
    max-height: 500px;
  }
}
