/*
 * Copyright 2023 (c) Jaguar Land Rover Ltd. All rights reserved.
 */

import { useCallback, useEffect, useRef, useState } from "react";
import type { Notification } from "context/notification.context";
import { randomID } from "utils/random";
import { XIcon } from "components/Icons";
import styles from "./Notifications.module.scss";

const displayDuration = 5000;

const typeMap = {
  default: {
    style: "",
  },
  success: {
    style: styles.success,
  },
  error: {
    style: styles.error,
  },
  warning: {
    style: styles.warning,
  },
};

interface NotificationItemProps {
  notification: Notification;
  onRemove: (id: string) => void;
}

export const NotificationItem = ({ notification, onRemove }: NotificationItemProps) => {
  const { id, type, title, body, expandOption, expandContent } = notification;
  const { style } = typeMap[type];
  const [expandButton, setExpandButton] = useState(false);

  const displayDurationTimeoutId = useRef<number>();

  const onMouseEnter = () => {
    clearTimeout(displayDurationTimeoutId.current);
  };

  const onMouseLeave = () => {
    displayDurationTimeoutId.current = window.setTimeout(removeItem, displayDuration / 2);
  };

  const removeItem = useCallback(() => {
    onRemove(id);
  }, [id, onRemove]);

  useEffect(() => {
    displayDurationTimeoutId.current = window.setTimeout(removeItem, displayDuration);
    return () => clearTimeout(displayDurationTimeoutId.current);
  }, [removeItem]);

  return (
    <div
      className={`${style} ${styles.notification} ${expandButton ? styles.expanded : ""}`}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
      role="log"
    >
      <div>
        <h5 className={styles.title}>{title}</h5>
        {body && <p>{body}</p>}
      </div>

      {expandOption && !expandButton && (
        <div className="text-center">
          <button
            className="btn btn-outline-dark "
            onClick={() => {
              setExpandButton(true);
              console.log(expandContent);
            }}
          >
            Expand
          </button>
        </div>
      )}

      {expandButton && (
        <div
          className={`${styles.errorContent} ${expandButton ? styles.errorContentExpanded : ""}`}
        >
          {typeof expandContent === "string" && expandContent}

          {Array.isArray(expandContent) && (
            <ul>
              {expandContent.map((message) => (
                <li className={styles.errorList} key={randomID()}>
                  {message}
                </li>
              ))}
            </ul>
          )}
        </div>
      )}

      <button
        aria-label="Close Notification"
        className={styles.close}
        onClick={removeItem}
        title="Close Notification"
      >
        <XIcon />
      </button>
    </div>
  );
};
