/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

import { CheckIcon, XIcon } from "components/Icons";
import { TableItemI } from "types/forgerock";

export const TableItem = ({ title, vehicleItem, usesIcons }: TableItemI) => {
  if (usesIcons) {
    return (
      <li className="list-group-item">
        <strong>{title}: </strong>
        {vehicleItem ? (
          <>
            <CheckIcon /> YES
          </>
        ) : (
          <>
            <XIcon fill="#e30000" size="32" /> NO
          </>
        )}
      </li>
    );
  }
  return (
    <li className="list-group-item">
      <strong>{title}: </strong>
      {vehicleItem}
    </li>
  );
};
