/*
 * Copyright (c) Jaguar Land Rover Ltd 2022. All rights reserved
 */

import { useState } from "react";
import { Card } from "components/Card";
import { ErrorMessage, Manifest, ManifestDataTableProps } from "types/data";
import { Popover, Tooltip } from "components/Overlays";
import { ErrorModal } from "components/Errors";
import { defaultTableData } from "./defaultTableData";

interface ManifestDataProps {
  vin?: string;
  manifest?: Manifest;
  obg?: boolean;
  errors?: ErrorMessage[];
  vehicleStatus?: string;
  vehicleArchitecture?: string;
}

export function ManifestData(props: ManifestDataProps) {
  const { vin, manifest, obg, errors, vehicleStatus, vehicleArchitecture } = props;
  const [errorMessages, setErrorMessages] = useState<ErrorMessage[]>([]);

  const handleErrorBtn = () => {
    setErrorMessages(errors ? errors : []);
  };

  const displayErrorBody = () => {
    if (errors?.length === 0 || vehicleStatus === "NE") {
      return "No errors on this vehicle.";
    }

    if (vehicleStatus === "PE") {
      return (
        <Popover
          clickIcon={false}
          label={<ErrorModal errorMessages={errorMessages} shouldUseFilter />}
        >
          <Tooltip label="View Previous Errors">
            <button className="btn btn-light border border-dark" onClick={() => handleErrorBtn()}>
              Previous Errors
            </button>
          </Tooltip>
        </Popover>
      );
    }

    return (
      <Popover
        clickIcon={false}
        label={<ErrorModal errorMessages={errorMessages} shouldUseFilter />}
      >
        <Tooltip label="View Errors">
          <button className="btn btn-light border border-dark" onClick={() => handleErrorBtn()}>
            Active Errors
          </button>
        </Tooltip>
      </Popover>
    );
  };

  const displayBody = () => {
    if (!vin) {
      return (
        <tr>
          <td>Enter a VIN to get feedback</td>
        </tr>
      );
    }

    return (
      <>
        <tr>
          <td>Vehicle Type</td>
          <td className="tableCenterCell text-break">{obg ? "OBG" : "OBA"}</td>
        </tr>
        <tr>
          <td>Vehicle Architecture</td>
          <td className="tableCenterCell text-break">{vehicleArchitecture}</td>
        </tr>
        <tr>
          <td className="align-middle">
            Feedback: <i>{vehicleStatus === "AE" ? "Errors" : "No Errors"}</i>
          </td>
          <td className="d-flex justify-content-center">{displayErrorBody()}</td>
        </tr>
        {defaultTableData.map(({ name, value }) => {
          return (
            <tr key={name}>
              <td>{name}</td>
              <td className="tableCenterCell text-break">
                {manifest ? manifest[value as keyof ManifestDataTableProps] : "-"}
              </td>
            </tr>
          );
        })}
      </>
    );
  };

  return (
    <Card hasBody={false} title="Manifest Data">
      <table aria-label="manifestTable" className="table table-bordered mb-0">
        <tbody>{displayBody()}</tbody>
      </table>
    </Card>
  );
}
