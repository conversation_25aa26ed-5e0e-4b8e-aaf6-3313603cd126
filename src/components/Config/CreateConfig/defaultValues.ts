/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

import { ConfigurationType } from "types/data";

export interface FormValuesProps {
  name: string;
  description: string;
  configurationType: ConfigurationType;
  file: string;
}

export const defaultFormValues: FormValuesProps = {
  name: "",
  description: "",
  configurationType: "",
  file: "",
};

export const configTypes = [
  { name: "Ruleset", value: "RULESET" },
  { name: "Query", value: "QUERY" },
  { name: "Signal Dictionary", value: "SIGNAL_DICTIONARY" },
  { name: "Diagnostic Dictionary", value: "DIAGNOSTIC_DICTIONARY" },
  { name: "Channel", value: "CHANNEL" },
  { name: "Bundle", value: "BUNDLE" }
];

export const filePath = {
  RULESET: "ruleset",
  QUERY: "query",
  SIGNAL_DICTIONARY: "signal",
  DIAGNOSTIC_DICTIONARY: "diagnostic",
  CHANNEL: "channel",
  BUNDLE: "bundle"
};
