/*
 * Copyright (c) Jaguar Land Rover Ltd 2022. All rights reserved
 */

import { ChangeEvent, FormEvent, useState } from "react";
import { ENV_CONTEXT } from "utils/constants";
import { post } from "services/fetch.service";
import { Card } from "components/Card";
import { useNotification } from "hooks/use-notification.hook";
import { ConfigurationType, CreateVehicleConfig, DisableOptionsProps } from "types/data";
import { configTypes, defaultFormValues, filePath, FormValuesProps } from "./defaultValues";

interface CreateConfigProps {
  disableOptions: DisableOptionsProps;
  getConfigFiles: () => void;
  disableUserInput: boolean;
  setDisableUserInput: (values: boolean) => void;
}

const getFilePath = (path: ConfigurationType) => {
  if (path !== "") return filePath[path];
};

export const CreateConfig = (props: CreateConfigProps) => {
  const { disableOptions, getConfigFiles, disableUserInput, setDisableUserInput } = props;
  const [configFormValues, setConfigFormValues] = useState<FormValuesProps>(defaultFormValues);
  const { notify } = useNotification();

  const handleFormChange = (event: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = event.target;
    setConfigFormValues((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };

  const handleConfigTypeSelection = (event: ChangeEvent<HTMLSelectElement>) => {
    const type = event.target.value as ConfigurationType;
    let userInputToggle: boolean;
    switch (type) {
      case "SIGNAL_DICTIONARY":
        userInputToggle = disableOptions.disableSignal;
        break;
      case "DIAGNOSTIC_DICTIONARY":
        userInputToggle = disableOptions.disableDiagnostic;
        break;
      case "CHANNEL":
        userInputToggle = disableOptions.disableChannel;
        break;
      default:
        userInputToggle = false;
        break;
    }
    setDisableUserInput(userInputToggle);
    setConfigFormValues((prevValues) => ({
      ...prevValues,
      configurationType: type,
    }));
  };

  const handleCreateConfigurationClick = async (event: FormEvent) => {
    event.preventDefault();

    if (Object.values(configFormValues).some((value) => !value)) {
      notify({
        type: "warning",
        title: "All required fields have not been entered",
      });
      return;
    }

    try {
      const { data, status } = await post<CreateVehicleConfig>(
        `${ENV_CONTEXT.cc_uri}/api/v1/config/${getFilePath(configFormValues.configurationType)}`,
        {
          ...configFormValues,
          file: JSON.parse(configFormValues.file),
        }
      );
      if (status === 201) {
        notify({
          type: "success",
          title: "Configuration Created",
        });
        getConfigFiles();
        setConfigFormValues(defaultFormValues);
      } else {
        if (data.message) {
          notify({
            type: "error",
            title: `${data.message}`,
          });
          return;
        } else if (data.messages) {
          data.messages.forEach((message: string) => {
            return notify({
              type: "error",
              title: `${message}`,
            });
          });
        }
      }
    } catch (error) {
      if (error instanceof Error) {
        if (error.name === "SyntaxError") {
          notify({
            type: "error",
            title: `JSON entered in file is not valid: ${configFormValues.file}`,
          });
        } else {
          notify({
            type: "error",
            title: error.message,
          });
        }
      }
    }
  };

  return (
    <Card title="Create Configuration">
      <form autoComplete="off" onSubmit={handleCreateConfigurationClick}>
        <div className="form-group">
          <label className="boldLabel" htmlFor="inputType">
            Type <span className="requiredRed">*</span>
          </label>
          <select
            aria-label="Default select example"
            className="form-select"
            defaultValue={configFormValues.configurationType}
            onChange={handleConfigTypeSelection}
          >
            <option value="">Please select a type</option>
            {configTypes.map((configType) => (
              <option key={configType.name} value={configType.value}>
                {configType.name}
              </option>
            ))}
          </select>
        </div>
        {disableUserInput && (
          <p className="mt-2" style={{ color: "red" }}>
            You do not have permission to create this config type
          </p>
        )}

        <div className="form-group mt-3">
          <label className="boldLabel" htmlFor="inputName">
            Name <span className="requiredRed">*</span>
          </label>
          <input
            className="form-control"
            disabled={disableUserInput}
            id="inputName"
            name="name"
            onChange={handleFormChange}
            placeholder="Enter a name"
            type="text"
            value={configFormValues.name}
          />
        </div>

        <div className="form-group mt-3">
          <label className="boldLabel" htmlFor="inputDescription">
            Description <span className="requiredRed">*</span>
          </label>
          <input
            className="form-control"
            disabled={disableUserInput}
            id="inputDescription"
            name="description"
            onChange={handleFormChange}
            placeholder="Enter a description"
            type="text"
            value={configFormValues.description}
          />
        </div>

        <div className="form-group mt-3">
          <label className="boldLabel" htmlFor="inputFile">
            JSON File <span className="requiredRed">*</span>
          </label>
          <textarea
            className="form-control"
            disabled={disableUserInput}
            id="inputFile"
            name="file"
            onChange={handleFormChange}
            placeholder="Enter valid JSON data"
            rows={10}
            value={configFormValues.file}
          ></textarea>
        </div>

        <span className="requiredRed">*Required</span>

        <div className="text-center">
          <button className="btn btn-dark mt-3" disabled={disableUserInput} type="submit">
            Create Configuration
          </button>
        </div>
      </form>
    </Card>
  );
};
