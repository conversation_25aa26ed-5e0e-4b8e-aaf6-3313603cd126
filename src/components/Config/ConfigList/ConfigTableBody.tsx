/*
 * Copyright (c) Jaguar Land Rover Ltd 2022. All rights reserved
 */
import { VehicleConfig } from "types/data";
import { randomID } from "utils/random";

interface ConfigTableBodyProps {
  configList: VehicleConfig[];
  handleInspectClick: (config: VehicleConfig) => void;
  handleSendConfigClick: (config: VehicleConfig) => void;
  isStatic?: boolean;
}

export function ConfigTableBody(props: ConfigTableBodyProps) {
  const { isStatic, configList, handleInspectClick, handleSendConfigClick } = props;
  return (
    <>
      {configList.map((configuration) => (
        <tr className={isStatic ? "table-secondary" : undefined} key={randomID()}>
          <td
            className="truncate"
            data-bs-placement="top"
            data-bs-toggle="tooltip"
            title={configuration.configurationType}
          >
            {configuration.configurationType}
          </td>
          <td
            data-bs-placement="top"
            data-bs-toggle="tooltip"
            style={{ wordWrap: "break-word", maxWidth: "160px", cursor: "help" }}
            title={configuration.name}
          >
            {configuration.name}
          </td>
          <td className="d-grid gap-1 text-nowrap">
            <button
              className="btn btn-primary m-1"
              data-bs-target="#configModal"
              data-bs-toggle="modal"
              data-testid={`inspect-${configuration.id}`}
              onClick={() => handleInspectClick(configuration)}
            >
              Inspect
            </button>
            <button
              className="btn btn-success m-1"
              data-testid={`send-${configuration.id}`}
              onClick={() => handleSendConfigClick(configuration)}
            >
              Send
            </button>
          </td>
        </tr>
      ))}
    </>
  );
}
