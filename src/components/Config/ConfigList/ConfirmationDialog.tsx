/*
 * Copyright (c) Jaguar Land Rover Ltd 2022. All rights reserved
 */

interface ConfirmationDialogProps {
  closeModalConfirmation: () => void;
  handleBtnClick: () => void;
  message: string;
  request: string;
}

export function ConfirmationDialog(props: ConfirmationDialogProps) {
  const { closeModalConfirmation, handleBtnClick, message, request } = props;
  return (
    <div className="modalDeleteConfig openDeleteMenu">
      <h5 className="modal-title mr-auto">
        Are you sure you want to {message} this {request}?
      </h5>
      <div>
        <button
          className="btn btn-primary mx-2"
          data-bs-dismiss="modal"
          data-bs-target="#configModal"
          onClick={() => handleBtnClick()}
          role="button"
        >
          Yes
        </button>
        <button
          className="btn btn-outline-primary"
          onClick={() => closeModalConfirmation()}
          role="button"
        >
          No
        </button>
      </div>
    </div>
  );
}
