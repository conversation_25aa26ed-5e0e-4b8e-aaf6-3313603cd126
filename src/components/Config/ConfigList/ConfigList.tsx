/*
 * Copyright (c) Jaguar Land Rover Ltd 2022. All rights reserved
 */

import { useCallback, useEffect, useState } from "react";
import { Spinner } from "components/Spinner";
import { ENV_CONTEXT } from "utils/constants";
import { SearchFilter } from "components/SearchFilter";
import { useModalHandler } from "hooks/use-modal-handler.hook";
import { del, get, post } from "services/fetch.service";
import { Card } from "components/Card";
import { Pagination } from "components/Pagination";
import { usePagination } from "hooks/use-pagination.hook";
import { defaultVehicleConfig, VehicleConfig } from "types/data";
import { useSearchFilter } from "hooks/use-search-filter.hook";
import { useNotification } from "hooks/use-notification.hook";
import { ConfigModal } from "./ConfigModal";
import { ConfigTableBody } from "./ConfigTableBody";

interface ConfigListProps {
  configFiles: VehicleConfig[];
  loadingConfigs: boolean;
  setLoadingConfigs: (val: boolean) => void;
  updateConfigs: () => void;
  vinEntered: string;
}

const staticConfigArray = ["diag_dictionary", "protocol_channel", "4.1.1 missing signals"];

export function ConfigList(props: ConfigListProps) {
  const { configFiles, loadingConfigs, setLoadingConfigs, updateConfigs, vinEntered } = props;

  const { useModal } = useModalHandler("configModal");

  const [staticConfigsIsVisible, setStaticConfigsIsVisible] = useState(true);
  const [loadingPreview, setLoadingPreview] = useState(false);
  const [staticConfigs, setStaticConfigs] = useState<VehicleConfig[]>([]);
  const [userInputConfigs, setUserInputConfigs] = useState<VehicleConfig[]>([]);
  const [previewData, setPreviewData] = useState<VehicleConfig>(defaultVehicleConfig[0]);
  const [isDeletable, setIsDeletable] = useState(false);
  const { notify } = useNotification();

  const { searchFilter, filteredItems } = useSearchFilter(userInputConfigs, "name");

  const { data, handlePageChange, page, rowsPerPage, setPage, setRowsPerPage, total } =
    usePagination(filteredItems);

  const sortConfigFiles = useCallback(() => {
    const localStaticConfigs: VehicleConfig[] = [];
    const localUserInputConfigs: VehicleConfig[] = [];

    configFiles?.forEach((config) => {
      if (staticConfigArray.includes(config.name)) {
        localStaticConfigs.push(config);
      } else {
        localUserInputConfigs.push(config);
      }
    });
    setUserInputConfigs([...localUserInputConfigs].reverse());
    setStaticConfigs(localStaticConfigs);
    setLoadingConfigs(false);
  }, [configFiles, setLoadingConfigs]);

  useEffect(() => {
    sortConfigFiles();
  }, [sortConfigFiles]);

  const handleInspectClick = async (configuration: VehicleConfig) => {
    if (staticConfigArray.includes(configuration.name)) {
      setIsDeletable(false);
    } else {
      setIsDeletable(true);
    }
    setLoadingPreview(true);
    try {
      useModal?.show();
      const { data, status } = await get<VehicleConfig>(
        `${ENV_CONTEXT.cc_uri}/api/v1/config/getbyid?id=${configuration.id}`
      );

      if (status === 200) {
        setPreviewData(data);
        setLoadingPreview(false);
      } else {
        notify({
          type: "error",
          title: "Error retrieving configuration",
        });
      }
    } catch (error) {
      if (error instanceof Error) {
        notify({
          type: "error",
          title: error.message,
        });
      }
    }
  };

  const handleSendConfigClick = async (configuration: VehicleConfig) => {
    if (!vinEntered) {
      notify({
        type: "warning",
        title: "Please Enter a VIN",
      });
      return;
    }
    try {
      const { status } = await post(`${ENV_CONTEXT.cc_uri}/api/v1/config/send`, {
        configurationID: configuration.id,
        vins: [vinEntered],
      });
      if (status === 200) {
        notify({
          type: "success",
          title: "Configuration Sent to " + vinEntered,
        });
      } else {
        notify({
          type: "error",
          title: "Configuration Not Sent to " + vinEntered,
        });
      }
    } catch (error) {
      if (error instanceof Error) {
        notify({
          type: "error",
          title: error.message,
        });
      }
    }
  };

  const handleDeleteCall = async (configuration: VehicleConfig) => {
    try {
      const { status } = await del(
        `${ENV_CONTEXT.cc_uri}/api/v1/config/deletebyid?id=${configuration.id}`
      );
      if (status === 200) {
        notify({
          type: "success",
          title: `Deleted ${configuration.name}`,
        });
        updateConfigs();
        if (useModal) useModal.hide();
      } else {
        notify({
          type: "error",
          title: `Unsuccessful Deletion of ${configuration.name}`,
        });
      }
    } catch (error) {
      if (error instanceof Error) {
        notify({
          type: "error",
          title: error.message,
        });
      }
    }
  };

  const handleUpdateCall = async (configuration: VehicleConfig, newFile: string) => {
    const stringify = false;
    try {
      const { status } = await post(
        `${ENV_CONTEXT.cc_uri}/api/v1/config/updatebyid?id=${configuration.id}`,
        newFile,
        stringify
      );
      if (status === 200) {
        notify({
          type: "success",
          title: `Updated ${configuration.name}`,
        });
        updateConfigs();
      } else {
        notify({
          type: "error",
          title: `Unsuccessful Update to ${configuration.name}`,
        });
      }
    } catch (error) {
      if (error instanceof Error) {
        notify({
          type: "error",
          title: error.message,
        });
      }
    }
  };

  const renderTableBody = () => {
    if (loadingConfigs) {
      return (
        <tr>
          <td colSpan={3} style={{ height: "60vh", fontSize: "1.2rem" }}>
            <Spinner />
          </td>
        </tr>
      );
    }
    if (staticConfigs.length === 0 && data.length === 0) {
      return (
        <tr>
          <td colSpan={3} style={{ height: "60vh", fontSize: "1.2rem" }}>
            No Configs Available
          </td>
        </tr>
      );
    }
    return (
      <>
        {staticConfigsIsVisible && (
          <ConfigTableBody
            configList={staticConfigs}
            handleInspectClick={handleInspectClick}
            handleSendConfigClick={handleSendConfigClick}
            isStatic
          />
        )}

        <ConfigTableBody
          configList={data}
          handleInspectClick={handleInspectClick}
          handleSendConfigClick={handleSendConfigClick}
        />
      </>
    );
  };

  return (
    <>
      <ConfigModal
        config={previewData}
        handleDeleteCall={handleDeleteCall}
        handleUpdateCall={handleUpdateCall}
        isDeletable={isDeletable}
        loadingPreview={loadingPreview}
      />

      <Card bodyStyles="text-center p-0 scroll-list" title="Deliver Configuration">
        <table className="table table-bordered table-response">
          <thead className="table-heading">
            <tr>
              <th>Type</th>
              <th>Name</th>
              <th>Action</th>
            </tr>
          </thead>
          <tbody className="align-middle">{renderTableBody()}</tbody>
        </table>
      </Card>

      <SearchFilter
        isConfig
        searchFilter={searchFilter}
        setStaticConfigsIsVisible={setStaticConfigsIsVisible}
        staticConfigsIsVisible={staticConfigsIsVisible}
      />

      <Pagination
        handlePageChange={handlePageChange}
        page={page}
        rowsPerPage={rowsPerPage}
        setPage={setPage}
        setRowsPerPage={setRowsPerPage}
        total={total}
      />
    </>
  );
}
