/*
 * Copyright (c) Jaguar Land Rover Ltd 2022. All rights reserved
 */

import { useRef, useState } from "react";
import J<PERSON><PERSON><PERSON><PERSON> from "react-json-pretty";
import { Spinner } from "components/Spinner";
import { VehicleConfig } from "types/data";
import { ConfirmationDialog } from "./ConfirmationDialog";

interface ConfigModalProps {
  config: VehicleConfig;
  handleDeleteCall: (config: VehicleConfig) => void;
  handleUpdateCall: (config: VehicleConfig, newFile: string) => void;
  isDeletable: boolean;
  loadingPreview: boolean;
}

export function ConfigModal(props: ConfigModalProps) {
  const { config, handleDeleteCall, handleUpdateCall, isDeletable, loadingPreview } = props;
  const [isOpen, setIsOpen] = useState(false);
  const [isEditable, setIsEditable] = useState(false);
  const textRef = useRef<HTMLDivElement>(null);

  const handleDeleteConfirmation = () => {
    setIsOpen(!isOpen);
  };

  const closeModalConfirmation = () => {
    setIsOpen(false);
    setIsEditable(false);
  };

  const handleDeleteAction = () => {
    closeModalConfirmation();
    handleDeleteCall(config);
  };

  const handleSaveAction = () => {
    setIsEditable(false);
    closeModalConfirmation();
    if (textRef.current?.textContent) handleUpdateCall(config, textRef.current.textContent);
  };

  const handleCancelClick = () => {
    setIsOpen(false);
    setIsEditable(!isEditable);
  };

  const handleEditClick = () => {
    setIsOpen(false);
    setIsEditable(!isEditable);
  };

  const handleSaveClick = () => {
    setIsOpen(!isOpen);
  };

  const renderButtons = () => {
    if (isDeletable && isEditable) {
      // return option to save the edited file if not in protected list
      return (
        <div className="text-end">
          <button
            className="btn btn-primary mb-3 mx-3"
            onClick={() => handleSaveClick()}
            role="button"
          >
            Save
          </button>
          <button
            className="btn btn-outline-primary mb-3"
            onClick={() => handleCancelClick()}
            role="button"
          >
            Cancel
          </button>
          {renderConfirmationModal(handleSaveAction, "save", "configuration")}
        </div>
      );
    } else if (isDeletable && !isEditable) {
      // return option to edit if not in protected list
      return (
        <div className="text-end">
          <button
            className="btn btn-primary mb-3 mx-3"
            onClick={() => handleEditClick()}
            role="button"
          >
            Edit
          </button>
          <button
            className="btn btn-danger  mb-3"
            onClick={() => handleDeleteConfirmation()}
            role="button"
          >
            Delete
          </button>
          {renderConfirmationModal(handleDeleteAction, "delete", "configuration")}
        </div>
      );
    } else {
      // return nothing as its in the protected list.
      return <></>;
    }
  };

  const renderConfirmationModal = (fn: () => void, message: string, request: string) => {
    if (isOpen) {
      return (
        <ConfirmationDialog
          closeModalConfirmation={closeModalConfirmation}
          handleBtnClick={fn}
          message={message}
          request={request}
        />
      );
    }
  };

  const renderJSONContent = () => {
    if (loadingPreview) return <Spinner />;
    else if (!loadingPreview && !isEditable) {
      // return reading mode
      return <JSONPretty data={config.file} id="json-pretty" />;
    } else {
      // return editing mode
      return (
        <div
          className="jsonEditBox"
          contentEditable
          id="jsonInput"
          ref={textRef}
          role="textbox"
          suppressContentEditableWarning={true}
        >
          <JSONPretty className="jsonEditor" data={config.file} id="json-pretty" />
        </div>
      );
    }
  };

  return (
    <div
      aria-hidden="true"
      aria-labelledby="configModalLabel"
      className="modal fade"
      id="configModal"
      tabIndex={-1}
    >
      <div className="modal-dialog modal-lg" role="document">
        <div className="modal-content">
          <div className="modal-header">
            <h5 className="modal-title" id="configModalLabel">
              {config.configurationType} - {config.name}
            </h5>
            <button
              aria-label="Close"
              className="btn-close"
              data-bs-dismiss="modal"
              onClick={() => closeModalConfirmation()}
              role="button"
            ></button>
          </div>
          <div className="modal-body">
            {renderButtons()}
            {renderJSONContent()}
          </div>
        </div>
      </div>
    </div>
  );
}
