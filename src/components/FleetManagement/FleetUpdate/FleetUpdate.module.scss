@use "src/styles/variables" as *;

.noVins {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 2rem;
  text-align: center;
  margin: 2rem 0;

  svg {
    color: $gray3;
  }

  p {
    font-weight: normal;
    color: $gray9;
    font-style: italic;
    margin: 0;
  }
}

.confirmationContainer {
  h1 {
    text-align: center;
    margin: 2rem 0.5rem;
    font-size: 1.4rem;
    font-weight: 600;
  }

  .changesToBeMade {
    display: flex;
    flex-direction: row;
    justify-content: center;
    gap: 6rem;
    background-color: $gray4;
    padding: 4rem 2rem;
    margin: 0 4rem;
    border-radius: 0.375rem;
  }

  .container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    p {
      margin: 0;
      text-align: center;
    }
    span {
      font-size: 0.9rem;
      margin-right: 0.6rem;
    }
    div:first-child {
      color: black;
      background-color: white;
      border-radius: 2rem;
      padding: 0.5rem 1rem;
      border: 1px solid black;

      &:hover {
        background-color: lightgrey;
      }
    }
  }

  @media (max-width: 992px) {
    .vinInfoItem {
      margin: 0 1rem;
      padding: 2rem 1rem;
    }
  }

  .submission {
    margin-top: 2rem;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    gap: 1rem;

    h2 {
      text-align: center;
      margin: 2rem 0;
      font-size: 1.2rem;
      font-weight: 600;
    }

    .submitButtons {
      display: flex;
      gap: 1rem;
    }
  }
}
