/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

import { useMutation } from "@tanstack/react-query";
import { useContext } from "react";
import chunk from "lodash/chunk";
import { CircleOff, HelpIcon, Minus, Plus, TickCircle } from "components/Icons";
import { Spinner } from "components/Spinner";
import { useNotification } from "hooks/use-notification.hook";
import { FleetContext } from "context/fleet.context";
import { Card } from "components/Card";
import { Tooltip } from "components/Overlays";
import { addFleetID, deleteFleetID } from "api/fleet-managment.api";
import styles from "./FleetUpdate.module.scss";
import { VinUpdateItem } from "./VinUpdateItem";

interface FleetUpdateProps {
  resetVins: () => void;
  refetchFleetVehicles: () => void;
  successState: boolean;
  setSuccessState: (state: boolean) => void;
  searchFleetLoading: boolean;
}

export const FleetUpdate = (props: FleetUpdateProps) => {
  const { resetVins, refetchFleetVehicles, successState, setSuccessState, searchFleetLoading } =
    props;
  const { notify } = useNotification();

  const { vinsToAdd, vinsToRemove } = useContext(FleetContext);

  const submitAddRequest = useMutation({ mutationFn: addFleetID });
  const submitRemoveRequest = useMutation({ mutationFn: deleteFleetID });

  const submitFleetId = async () => {
    try {
      const addBatch = chunk(vinsToAdd, 100);
      const removeBatch = chunk(vinsToRemove, 100);

      await Promise.all([
        ...addBatch.map((batch) => submitAddRequest.mutateAsync({ vins: batch, fleetId: "MLAP" })),
        ...removeBatch.map((batch) => submitRemoveRequest.mutateAsync(batch)),
      ]);

      refetchFleetVehicles();
      setSuccessState(true);
      resetVins();
    } catch {
      notify({
        type: "error",
        title: "Error updating fleet",
      });
    }
  };

  if (submitAddRequest.isPending || submitRemoveRequest.isPending || searchFleetLoading) {
    return (
      <Card bodyStyles="mb-3" hasHeader={false}>
        <Spinner />
      </Card>
    );
  }

  if (!vinsToAdd.length && !vinsToRemove.length && !successState && !searchFleetLoading) {
    return (
      <Card bodyStyles="mb-3" hasHeader={false}>
        <div className={styles.noVins}>
          <CircleOff size="8rem" />
          <p>Please enter some VINs to update MLAP Fleet</p>
        </div>
      </Card>
    );
  }

  if (successState) {
    return (
      <Card bodyStyles="mb-3" hasHeader={false}>
        <div className={styles.noVins}>
          <TickCircle color="green" size="9rem" />
          <div>
            <p>MLAP fleet has been updated with the provided vehicles</p>
            <p>Please enter new VINs to update again</p>
          </div>
        </div>
      </Card>
    );
  }

  return (
    <Card bodyStyles="mb-3" hasHeader={false}>
      <div className={styles.confirmationContainer}>
        <h1>
          The following changes will be made to the MLAP fleet
          <Tooltip
            label={
              <div>
                <p>{`${vinsToAdd.length} new VIN${
                  vinsToAdd.length == 1 ? "" : "s"
                } will be added to the MLAP fleet from the uploaded CSV.`}</p>
                <p>{`${vinsToRemove.length} VIN${
                  vinsToRemove.length == 1 ? "" : "s"
                } will be removed from the MLAP fleet as they are not present in the CSV.`}</p>
              </div>
            }
          >
            <HelpIcon color="black" size={40} />
          </Tooltip>
        </h1>
        <div className={styles.changesToBeMade}>
          {vinsToAdd.length > 0 && (
            <VinUpdateItem
              icon={<Plus size="40" />}
              text={`VIN${vinsToAdd.length == 1 ? "" : "s"} will be added`}
              vinArray={vinsToAdd}
            />
          )}

          {vinsToRemove.length > 0 && (
            <VinUpdateItem
              icon={<Minus size="40" />}
              text={`VIN${vinsToRemove.length == 1 ? "" : "s"} will be removed`}
              vinArray={vinsToRemove}
            />
          )}
        </div>

        <div className={styles.submission}>
          <h2>Are you sure you want to update these vehicles?</h2>

          <div className={styles.submitButtons}>
            <button
              className="btn btn-outline-secondary noWrap"
              onClick={() => resetVins()}
              type="submit"
            >
              Cancel
            </button>
            <button className="btn btn-dark noWrap" onClick={() => submitFleetId()} type="submit">
              Submit
            </button>
          </div>
        </div>
      </div>
    </Card>
  );
};
