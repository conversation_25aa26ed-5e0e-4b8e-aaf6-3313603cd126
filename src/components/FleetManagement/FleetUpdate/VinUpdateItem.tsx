/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

import { ReactElement } from "react";
import { Popover } from "components/Overlays";
import { Dropdown } from "components/Dropdown";
import { DownArrow } from "components/Icons";
import styles from "./FleetUpdate.module.scss";

interface VinUpdateItemProps {
  icon: ReactElement;
  vinArray: string[];
  text: string;
}

export const VinUpdateItem = ({ icon, vinArray, text }: VinUpdateItemProps) => {
  return (
    <div className={styles.container}>
      {icon}
      <p>{`${vinArray.length} ${text}`}</p>
      {vinArray.length > 0 && (
        <Popover clickIcon={false} label={<Dropdown items={vinArray} />}>
          <span>View VINs</span>
          <DownArrow size={20} />
        </Popover>
      )}
    </div>
  );
};
