/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

import { useQuery } from "@tanstack/react-query";
import { Card } from "components/Card";
import { Spinner } from "components/Spinner";
import { Car, DownArrow } from "components/Icons";
import { VehicleIdentity } from "types/fleet-management";
import { Popover } from "components/Overlays";
import { Dropdown } from "components/Dropdown";
import { getFleetCount } from "api/fleet-managment.api";
import styles from "./FleetCounter.module.scss";

interface FleetCounterProps {
  fleetVehicles?: VehicleIdentity[];
}

export const FleetCounter = ({ fleetVehicles }: FleetCounterProps) => {
  const { data: fleetCount, isLoading } = useQuery({
    queryKey: ["VehicleCount", fleetVehicles],
    queryFn: () => getFleetCount("MLAP"),
    enabled: !!fleetVehicles
  });

  if (isLoading || !fleetVehicles) {
    return (
      <Card isGrey title="Vehicles in MLAP Fleet">
        <Spinner />
      </Card>
    );
  }

  return (
    <Card isGrey title="Vehicles in MLAP Fleet">
      {fleetCount?.data ? (
        <div className={styles.count}>
          <Car size="3rem" />
          <p>{`${fleetCount?.data?.data.count} Vehicles`}</p>
        </div>
      ) : null}
      <div className={styles.vehiclesButton}>
        <Popover
          clickIcon={false}
          label={<Dropdown items={fleetVehicles?.map((vehicle) => vehicle.identity.vin) ?? []} />}
        >
          <span>View VINs</span>
          <DownArrow size={20} />
        </Popover>
      </div>
    </Card>
  );
};
