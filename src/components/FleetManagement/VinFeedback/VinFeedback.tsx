/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

import { useContext } from "react";
import { HelpIcon, InfoSquare, WarningTriangleIcon } from "components/Icons";
import { FleetContext } from "context/fleet.context";
import { Tooltip } from "components/Overlays";
import { FeedbackItem } from "./FeedbackItem";

interface VinFeedbackProps {
  loading?: boolean;
  visible?: boolean;
}

export const VinFeedback = ({ visible = true }: VinFeedbackProps) => {
  const { duplicateVins, invalidVins } = useContext(FleetContext);
  return (
    <>
      <FeedbackItem
        icon={<WarningTriangleIcon />}
        text={`Invalid VIN${invalidVins.length == 1 ? "" : "s"}`}
        toolTip={
          <Tooltip
            label="Invalid VINs either have the wrong length, duplicated VINs or already contain the
MLAP fleet Id."
          >
            <HelpIcon color="black" size={20} />
          </Tooltip>
        }
        vinArray={invalidVins}
        visible={visible}
      />

      <FeedbackItem
        icon={<InfoSquare color="#536878" size="32" />}
        text={`VIN${duplicateVins.length == 1 ? "" : "s"} already exist${
          duplicateVins.length == 1 ? "s" : ""
        } in MLAP`}
        vinArray={duplicateVins}
        visible={visible}
      />
    </>
  );
};
