/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

import { ReactElement, ReactNode } from "react";
import { Card } from "components/Card";
import { Popover } from "components/Overlays";
import { Dropdown } from "components/Dropdown";
import styles from "./VinFeedback.module.scss";

interface DisplayFeedbackProps {
  icon: ReactElement;
  visible: boolean;
  vinArray: string[];
  text: string;
  toolTip?: ReactNode;
}

export const FeedbackItem = ({ icon, visible, vinArray, text, toolTip }: DisplayFeedbackProps) => {
  if (visible && vinArray.length > 0) {
    return (
      <div className={styles.fadein}>
        <Card hasHeader={false}>
          <div className={styles.container}>
            <div className={styles.textInfo}>
              <p>
                {vinArray.length} {text}
              </p>
              {toolTip}
            </div>
            <Popover label={<Dropdown items={vinArray} />}>{icon}</Popover>
          </div>
        </Card>
      </div>
    );
  }
};
