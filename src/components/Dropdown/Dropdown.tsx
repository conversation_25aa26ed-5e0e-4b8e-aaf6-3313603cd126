/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

import { useState } from "react";
import { randomID } from "utils/random";
import styles from "./Dropdown.module.scss";

interface DropdownProps {
  items: string[];
}

export const Dropdown = ({ items }: DropdownProps) => {
  const [searchQuery, setSearchQuery] = useState("");

  const filteredItems = items.filter((vin) =>
    vin.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className={styles.dropdown}>
      <input
        className="form-control mb-1"
        onChange={(changeEvent) => setSearchQuery(changeEvent.target.value)}
        placeholder="Search VIN"
        type="text"
        value={searchQuery}
      />
      <ul className="list-group">
        {filteredItems.map((item) => (
          <li className="list-group-item text-break" key={randomID()}>
            {item}
          </li>
        ))}
      </ul>
    </div>
  );
};
