/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

interface SearchFilterProps {
  searchFilter: ({ target }: { target: HTMLInputElement }) => void;
  staticConfigsIsVisible?: boolean;
  setStaticConfigsIsVisible?: (val: boolean) => void;
  isConfig?: boolean;
}

export const SearchFilter = (props: SearchFilterProps) => {
  const { searchFilter, staticConfigsIsVisible, setStaticConfigsIsVisible, isConfig } = props;

  const handleHideConfigs = () => {
    if (setStaticConfigsIsVisible) setStaticConfigsIsVisible(!staticConfigsIsVisible);
  };
  return (
    <div className="mt-2 mb-3 p-3 d-flex flex-row justify-content-center bg-light rounded-pill">
      <div>
        <input
          className="form-control"
          onChange={searchFilter}
          placeholder="Type to filter list..."
          type="text"
        />
      </div>
      {isConfig && (
        <button className="btn btn-outline-dark mx-1" onClick={handleHideConfigs} role="button">
          {staticConfigsIsVisible ? "Hide" : "Show"} Static Configs
        </button>
      )}
    </div>
  );
};
