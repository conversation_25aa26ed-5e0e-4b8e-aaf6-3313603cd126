/*
 * Copyright (c) Jaguar Land Rover Ltd 2022. All rights reserved
 */

import { useState } from "react";
import { ErrorModal } from "components/Errors";
import { Card } from "components/Card";
import { ErrorMessage, Vehicle } from "types/data";
import { Popover, Tooltip } from "components/Overlays";
import { ClickIcon } from "components/Icons";
import { randomID } from "utils/random";
import { ErrorSymbol } from "./ErrorSymbol";

interface ControlTableProps {
  vehicles: Vehicle[];
  removeVin: (vin: string) => void;
  searchDigitalVehicle: (clickType?: "refresh") => void;
}

export function ControlTable(props: ControlTableProps) {
  const { vehicles, removeVin, searchDigitalVehicle } = props;
  const [errorMessages, setErrorMessages] = useState<ErrorMessage[]>([]);
  const [title, setTitle] = useState("");

  const displayTableItems = () => {
    if (vehicles?.length === 0) {
      return (
        <tr>
          <td colSpan={7}>No VINS Added</td>
        </tr>
      );
    }

    return vehicles.map((vehicle) => {
      const { enableVA, allowWiFi, enableMirroring, enablePushData } =
        vehicle.applications.vehicleAnalytics.status;
      const va = vehicle.applications.vehicleAnalytics;
      return (
        <tr className="align-middle" key={vehicle.identity.vin}>
          <td className="tableSymbol">
            <Popover clickIcon={false} label={<ErrorModal errorMessages={errorMessages} />}>
              <Tooltip label={title}>
                <ErrorSymbol setErrorMessages={setErrorMessages} setTitle={setTitle} va={va} />
              </Tooltip>
            </Popover>
          </td>
          <td>
            <div className="obgPill">
              <p>{vehicle.identity.vin}</p>
              <div
                className={vehicle?.applications?.obg?.isEnabled ? "text-success" : "text-danger"}
              >
                {vehicle?.applications?.obg?.isEnabled ? "OBG" : "OBA"}
              </div>
            </div>
          </td>
          {[enableVA, allowWiFi, enablePushData, enableMirroring].map((value) => {
            return (
              <td key={randomID()}>
                <img
                  alt={`${value ? "green" : "red"} light`}
                  height="22"
                  src={`${value ? "green" : "red"}_light.png`}
                  width="22"
                />
              </td>
            );
          })}
          <td>
            <button
              aria-label="removeVin"
              className="btn-close"
              onClick={() => removeVin(vehicle.identity.vin)}
              role="button"
            />
          </td>
        </tr>
      );
    });
  };

  return (
    <>
      <Card hasBody={false} title="Selected Vehicles">
        <table
          aria-label="control-table"
          className="table table-bordered text-center table-heading mb-0"
        >
          <thead>
            <tr>
              <th>
                <ClickIcon size={24} />
              </th>
              <th className="th-vin">VIN</th>
              <th className="th-control">VA</th>
              <th className="th-control">WiFi</th>
              <th className="th-control">Push</th>
              <th className="th-control">Mirroring</th>
              <th className="th-button"></th>
            </tr>
          </thead>
          <tbody>{displayTableItems()}</tbody>
        </table>
        <div className="text-center my-2">
          <button
            className="btn btn-dark"
            onClick={() => searchDigitalVehicle("refresh")}
            type="button"
          >
            Refresh
          </button>
        </div>
      </Card>
    </>
  );
}
