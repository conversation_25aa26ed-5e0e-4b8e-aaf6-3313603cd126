/*
 * Copyright (c) Jaguar Land Rover Ltd 2022. All rights reserved
 */

import { useEffect, useState } from "react";
import { useModalHandler } from "hooks/use-modal-handler.hook";
import { ErrorMessage, VehicleAnalytics } from "types/data";
import { CheckIcon, ErrorCircleIcon, WarningTriangleIcon } from "components/Icons";

interface ErrorSymbolProps {
  setErrorMessages: (val: ErrorMessage[]) => void;
  setTitle: (val: string) => void;
  va: VehicleAnalytics;
}

const defaultStatus = {
  warning: false,
  error: false,
  allOk: false,
};

export function ErrorSymbol({ setErrorMessages, setTitle, va }: ErrorSymbolProps) {
  const [messages, setMessages] = useState<ErrorMessage[]>([]);
  const [indicator, setIndicator] = useState(defaultStatus);

  const { useModal } = useModalHandler("errorModal");

  useEffect(() => {
    setIndicator(defaultStatus);
    setMessages(va.errorMessages);
    if (va.manifest?.vaConfigAcceptErrorState === "PE") {
      setTitle("Previous Errors Found. Click to Display");
      setIndicator((prevState) => ({ ...prevState, warning: true }));
    } else if (va.manifest?.vaConfigAcceptErrorState === "AE") {
      setTitle("Errors Detected. Click to Display");
      setIndicator((prevState) => ({ ...prevState, error: true }));
    } else {
      setTitle("All Good With This Vehicle");
      setIndicator((prevState) => ({ ...prevState, allOk: true }));
    }
  }, [va, setTitle]);

  const handleIconClick = () => {
    setErrorMessages(messages);
    useModal?.show();
  };

  return (
    <button className="errorSymbol" onClick={() => handleIconClick()}>
      {indicator.warning && <WarningTriangleIcon />}
      {indicator.error && <ErrorCircleIcon />}
      {indicator.allOk && <CheckIcon />}
    </button>
  );
}
