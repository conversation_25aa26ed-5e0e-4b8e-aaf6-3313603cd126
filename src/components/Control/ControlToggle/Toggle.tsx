/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

import { ToggleProps } from "types/toggles";

export const Toggle = ({ isDisabled, onChange, toggle }: ToggleProps) => {
  return (
    <tr>
      <td className="toggle-names">{toggle.title}</td>
      <td>
        <div className="btn-group" onChange={onChange} role="group">
          <input
            autoComplete="off"
            className="btn-check"
            disabled={isDisabled}
            id={`${toggle.name}-disable`}
            name={toggle.name}
            type="radio"
            value="true"
          />
          <label className="btn btn-outline-success" htmlFor={`${toggle.name}-disable`}>
            True
          </label>

          <input
            autoComplete="off"
            className="btn-check"
            defaultChecked={toggle.action === null}
            disabled={isDisabled}
            id={`${toggle.name}-n`}
            name={toggle.name}
            type="radio"
            value={undefined}
          />
          <label className="btn btn-outline-secondary" htmlFor={`${toggle.name}-n`}>
            <img
              alt={`${toggle.title} button`}
              className="d-inline-block align-top"
              height="22"
              src="deselect_icon.png"
              width="22"
            />
          </label>

          <input
            autoComplete="off"
            className="btn-check"
            disabled={isDisabled}
            id={`${toggle.name}-enable`}
            name={toggle.name}
            type="radio"
            value="false"
          />
          <label className="btn btn-outline-danger" htmlFor={`${toggle.name}-enable`}>
            False
          </label>
        </div>
      </td>
    </tr>
  );
};
