/*
 * Copyright (c) Jaguar Land Rover Ltd 2022. All rights reserved
 */

import { ENV_CONTEXT } from "utils/constants";
import { post } from "services/fetch.service";
import { ToggleItemProps } from "types/toggles";
import { useNotification } from "hooks/use-notification.hook";
import { randomID } from "utils/random";

interface ConfirmationModalProps {
  controls: ToggleItemProps[];
  vins: string[];
}

export function ConfirmationModal({ controls, vins }: ConfirmationModalProps) {
  const { notify } = useNotification();
  const postControls = async () => {
    try {
      const { status } = await post(`${ENV_CONTEXT.cc_uri}/controls/va`, {
        controls,
        vins,
      });
      if (status === 201) {
        notify({
          type: "success",
          title: "Controls sent successfully",
        });
      } else {
        notify({
          type: "error",
          title: "Controls not sent",
        });
      }
    } catch (error) {
      if (error instanceof Error) {
        notify({
          type: "error",
          title: error.message,
        });
      }
    }
  };

  return (
    <div
      aria-hidden="true"
      aria-label="confirmationModalLabel"
      className="modal fade"
      id="confirmationModal"
      tabIndex={-1}
    >
      <div className="modal-dialog" role="document">
        <div className="modal-content">
          <div className="modal-header">
            <h5 className="modal-title" id="confirmationModalLabel">
              Control Toggle Confirmation
            </h5>
            <button
              aria-label="Close"
              className="btn-close"
              data-bs-dismiss="modal"
              type="button"
            />
          </div>
          <div className="modal-body">
            <p>Are you sure you want to update the following controls?</p>

            <ul style={{ listStyleType: "none" }}>
              {controls.map((control) => (
                <li key={randomID()}>{`${control.name} = ${control.action}`}</li>
              ))}
            </ul>
            <div className="d-flex justify-content-center gap-5 my-2">
              <button
                aria-label="continueBtn"
                className="btn btn-success"
                data-bs-dismiss="modal"
                onClick={() => postControls()}
                type="button"
              >
                Continue
              </button>
              <button
                aria-label="cancelBtn"
                className="btn btn-dark"
                data-bs-dismiss="modal"
                type="button"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
