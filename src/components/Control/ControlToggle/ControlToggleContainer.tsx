/*
 * Copyright (c) Jaguar Land Rover Ltd 2022. All rights reserved
 */

import { useState } from "react";

import { useModalHandler } from "hooks/use-modal-handler.hook";
import { Card } from "components/Card";
import { ToggleItemProps } from "types/toggles";
import { useNotification } from "hooks/use-notification.hook";
import { usePermissions } from "hooks/use-permissions";
import { Toggle } from "./Toggle";
import { ConfirmationModal } from "./ConfirmationModal";

interface ControlToggleContainerProps {
  validatedVins: string[];
}

const defaultToggles = [
  { action: null, name: "enableVA", title: "Enable VA" },
  { action: null, name: "allowWiFi", title: "Enable WiFi" },
  { action: null, name: "enablePushData", title: "Allow Push Data" },
  { action: null, name: "enableMirroring", title: "Enable Mirroring" }
];

export const ControlToggleContainer = ({ validatedVins }: ControlToggleContainerProps) => {
  const { useModal } = useModalHandler("confirmationModal");
  const { hasPermission } = usePermissions();
  const controlsDisabled = !hasPermission("ApplyControls");

  const [toggles, setToggles] = useState<ToggleItemProps[]>(defaultToggles);
  const [selectedControls, setSelectedControls] = useState<ToggleItemProps[]>([]);
  const [selectedVins, setSelectedVins] = useState<string[]>([]);
  const { notify } = useNotification();

  const handleToggleChange = (val: string, name: string) => {
    setToggles(
      [...toggles].map((object) => {
        if (object.name === name) {
          return {
            ...object,
            action: val ? val : null
          };
        }
        return object;
      })
    );
  };

  const handleConfirmation = (controls: ToggleItemProps[], vins: string[]) => {
    if (vins.length === 0) {
      notify({ type: "error", title: "No VINs uploaded" });
    } else if (controls.length === 0) {
      notify({ type: "error", title: "No Controls selected" });
    } else {
      useModal?.show();
      setSelectedControls(controls);
      setSelectedVins(vins);
    }
  };

  return (
    <>
      <ConfirmationModal controls={selectedControls} vins={selectedVins} />
      <Card hasBody={false} isGrey={controlsDisabled} title="Control Toggle Selector">
        <table aria-label="toggle-table" className="table toggles text-center mt-1 mb-0">
          <tbody>
            {toggles.map((toggle) => {
              return (
                <Toggle
                  isDisabled={controlsDisabled}
                  key={toggle.name}
                  onChange={(event) => handleToggleChange(event.target.value, toggle.name)}
                  toggle={toggle}
                />
              );
            })}
          </tbody>
        </table>
        <div className="text-center">
          <button
            className="btn btn-outline-dark my-2 disabled-button"
            disabled={controlsDisabled}
            onClick={() => handleConfirmation(toggles, validatedVins)}
            type="submit"
          >
            Update Vehicles
          </button>
          {controlsDisabled && (
            <p className="text-danger">
              <i>
                You do not have permission to update controls.
                <br /> Please contact #halo-support on Slack to get access.
              </i>
            </p>
          )}
        </div>
      </Card>
    </>
  );
};
