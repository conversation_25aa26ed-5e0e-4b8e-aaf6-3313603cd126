/*
 * Copyright (c) Jaguar Land Rover Ltd 2022. All rights reserved
 */

import { useState } from "react";
import { SelectNumber } from "./SelectNumber";

interface PaginationProps {
  handlePageChange: (val: number) => void;
  setPage: (val: number) => void;
  setRowsPerPage: (val: number) => void;
  page: number;
  rowsPerPage: number;
  total: number;
}

export const Pagination = (props: PaginationProps) => {
  const { handlePageChange, page, rowsPerPage, setPage, setRowsPerPage, total } = props;
  const [showingAll, setShowingAll] = useState(false);

  const handleShowAll = () => {
    if (showingAll) {
      setRowsPerPage(10);
      setShowingAll(false);
    } else {
      setRowsPerPage(total);
      setShowingAll(true);
    }
    setPage(0);
  };

  const showPageNumber = () => {
    let currentNumber = total;
    if (page * rowsPerPage + rowsPerPage < total) {
      currentNumber = page * rowsPerPage + rowsPerPage;
    }
    return `${page * rowsPerPage + 1} - ${currentNumber} of ${total}`;
  };

  const showRowSelect = () => {
    return (
      <SelectNumber onChange={setRowsPerPage} options={[10, 20, 50, 100]} value={rowsPerPage} />
    );
  };

  return (
    <nav aria-label="table-navigation">
      <ul className="pagination justify-content-center align-items-center gap-2">
        <li className="page-item">
          <button
            className="btn btn-outline-secondary"
            onClick={() => handleShowAll()}
            role="button"
          >
            Show {showingAll ? "Less" : "All"}
          </button>
        </li>

        <li className="page-item">
          <button
            className="btn btn-outline-dark"
            disabled={page <= 0}
            onClick={() => handlePageChange(-1)}
            role="button"
          >
            Previous
          </button>
        </li>

        <li className="page-item">
          <button
            className="btn btn-outline-dark"
            disabled={page * rowsPerPage + rowsPerPage > total || rowsPerPage === total}
            onClick={() => handlePageChange(1)}
            role="button"
          >
            Next
          </button>
        </li>
        <li className="page-item">{showRowSelect()}</li>
      </ul>
      <p className="text-center m-0">{showPageNumber()}</p>
    </nav>
  );
};
