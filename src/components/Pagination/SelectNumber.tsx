/*
 * Copyright 2023 (c) Jaguar Land Rover Ltd. All rights reserved.
 */

interface SelectNumberProps {
  value: number;
  options: number[];
  onChange: (val: number) => void;
}

export const SelectNumber = (props: SelectNumberProps) => {
  const { value, options, onChange } = props;
  return (
    <select
      className="form-select"
      onChange={({ target }) => onChange(parseInt(target.value))}
      style={{ border: "1px solid #6c757d" }}
      value={value}
    >
      {options.map((option) => {
        return (
          <option key={option} value={option}>
            {option}
          </option>
        );
      })}
    </select>
  );
};
