/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

import { useMutation } from "@tanstack/react-query";
import { useState } from "react";
import { DPStatus } from "types/data";
import { Spinner } from "components/Spinner";
import { Select } from "components/Form";
import { updateDpStatus } from "api/data-product-manager.api";
import { useNotification } from "hooks/use-notification.hook";
import { Card } from "components/Card";

interface StatusPopoverI {
  dataProductId: string;
  status: string;
  refetch: () => void;
}

export const StatusPopover = ({ dataProductId, status, refetch }: StatusPopoverI) => {
  const { notify } = useNotification();
  const [updatedStatus, setUpdatedStatus] = useState<DPStatus>();

  const statusRequest = useMutation({
    mutationFn: updateDpStatus,
    onSuccess: () => {
      notify({
        type: "success",
        title: `${dataProductId} status updated to ${updatedStatus}`,
      });
      refetch();
    },
    onError: () => {
      notify({
        type: "warning",
        title: "Data Product Status update encountered an error",
      });
    },
  });

  const handleStatusUpdate = async () => {
    if (updatedStatus) {
      await statusRequest.mutateAsync({
        dpId: dataProductId,
        status: updatedStatus,
      });
    }
  };

  const disableStatusButton = () => {
    return !updatedStatus || status === updatedStatus;
  };

  if (statusRequest.isPending) {
    return <Spinner />;
  }
  return (
    <Card cardStyles="" hasHeader={false}>
      <div className="d-flex flex-row align-items-end gap-3">
        <Select
          label="Update Status"
          onChange={setUpdatedStatus}
          options={[
            { label: "ROLLOUT", value: "ROLLOUT" },
            { label: "TESTING", value: "TESTING" },
          ]}
          value={updatedStatus}
        />
        <button
          className="btn btn-dark"
          disabled={disableStatusButton()}
          onClick={handleStatusUpdate}
          type="button"
        >
          Submit
        </button>
      </div>
    </Card>
  );
};
