/*
 * Copyright (c) Jaguar Land Rover Ltd 2022. All rights reserved
 */

import <PERSON><PERSON><PERSON><PERSON><PERSON> from "react-json-pretty";
import { Delta } from "types/data";
import { Card } from "components/Card";
import { TickCircle } from "components/Icons";
import { randomID } from "utils/random";
import styles from "./InspectModal.module.scss";

interface InspectModalProps {
  modalData: Delta;
}

const eligibilityCriteriaKeys = [
  { name: "Brand", key: "brand", category: "assets" },
  { name: "Model Range", key: "modelRange", category: "assets" },
  { name: "Model Year", key: "modelYear", category: "assets" },
  { name: "Fuel Type", key: "fuelType", category: "assets" },
  { name: "Fleet ID", key: "fleetId", category: "assets" },
  { name: "Tags", key: "tags", category: "tags" }
];

export function InspectModal({ modalData }: InspectModalProps) {
  const renderEligibilityCriteria = () => {
    if (modalData.eligibilityCriteria) {
      return (
        <ul className="list-group">
          {eligibilityCriteriaKeys.map(({ name, key, category }) => {
            const values =
              category === "assets"
                ? modalData.eligibilityCriteria.assets &&
                  modalData.eligibilityCriteria.assets[
                    key as keyof typeof modalData.eligibilityCriteria.assets
                  ]
                : modalData.eligibilityCriteria.tags &&
                  modalData.eligibilityCriteria.tags[
                    key as keyof typeof modalData.eligibilityCriteria.tags
                  ];

            return (
              <li className={`list-group-item ${styles.listItem}`} key={randomID()}>
                <strong>{name}: </strong>
                {!values || !values.length ? <span className="na">N/A</span> : values.join(", ")}
              </li>
            );
          })}
        </ul>
      );
    } else {
      return (
        <Card bodyStyles={styles.nullEligibility} hasHeader={false}>
          <p>All vehicles are eligible for this Data Product</p>
          <TickCircle />
        </Card>
      );
    }
  };

  const renderPermissions = () => {
    return (
      <ul className="list-group">
        {modalData.permissionList.map((permission) => {
          return (
            <li className="list-group-item" key={randomID()}>
              {permission}
            </li>
          );
        })}
      </ul>
    );
  };

  return (
    <div
      aria-hidden="true"
      aria-label="inspectModalLabel"
      className="modal fade"
      id="inspectModal"
      tabIndex={-1}
    >
      <div className={`modal-dialog modal-lg ${styles.modalContainer}`} role="document">
        <div className="modal-content">
          <div className="modal-header">
            <h5 className="modal-title" id="inspectModalLabel">
              <b>Data Product:</b> {modalData.dataProductId}
            </h5>
            <button
              aria-label="Close"
              className="btn-close"
              data-bs-dismiss="modal"
              type="button"
            />
          </div>
          <div className="modal-body">
            <h3>Eligibility Criteria</h3>
            <p className="description text-center mb-2">
              Data Products will only be applied to vehicles that meet the following criteria:
            </p>
            {renderEligibilityCriteria()}
            <h3>Permissions</h3>
            <p className="description text-center mb-2">
              Data Products can only be applied to vehicles with the following permissions:
            </p>
            {renderPermissions()}
            <h3>Queries</h3>
            <p className="description text-center mb-2">
              The following queries are applied to the vehicle:
            </p>
            <div className="scroll-list">
              <JSONPretty data={modalData.queries} id="json-pretty" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
