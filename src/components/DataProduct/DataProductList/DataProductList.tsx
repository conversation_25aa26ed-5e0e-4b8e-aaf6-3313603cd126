/*
 * Copyright (c) Jaguar Land Rover Ltd 2022. All rights reserved
 */

import { useContext, useState } from "react";
import { useMutation, useQuery } from "@tanstack/react-query";
import { Spinner } from "components/Spinner";
import { useModalHandler } from "hooks/use-modal-handler.hook";
import { useSearchFilter } from "hooks/use-search-filter.hook";
import { Card } from "components/Card";
import { useNotification } from "hooks/use-notification.hook";
import { SearchFilter } from "components/SearchFilter";
import { ApplyDataProduct, DataProductPolicy, defaultDelta, Delta, DPResults } from "types/data";
import { Pagination } from "components/Pagination";
import { usePagination } from "hooks/use-pagination.hook";
import { VehicleContext } from "context/vehicle.context";
import { getDateTime, sortByDate } from "utils/dates";
import { getVehicles, updateDataProductPolicies } from "api/digital-vehicle.api";
import { Popover } from "components/Overlays";
import { randomID } from "utils/random";
import { applyDataProduct } from "api/data-product-delivery.api";
import { getDeltas } from "api/data-product-manager.api";
import { getEnvName } from "utils/constants";
import { AuthContext } from "context/auth.context";
import { InspectModal } from "./InspectModal/InspectModal";
import { StatusPopover } from "./StatusPopover";

export function DataProductList() {
  const { vehicles, setVehicles } = useContext(VehicleContext);

  const [modalData, setModalData] = useState<Delta>(defaultDelta);
  const [sendingRequest, setSendingRequest] = useState(new Set());
  const { roles } = useContext(AuthContext);

  const inspectModal = useModalHandler("inspectModal");
  const { notify } = useNotification();

  const {
    data: deltas,
    isLoading: isLoadingDeltas,
    refetch: queryDataProducts,
  } = useQuery({
    queryKey: ["get-dps"],
    queryFn: () => getDeltas(),
  });

  const { searchFilter, filteredItems } = useSearchFilter<Delta>(
    deltas?.data.sort(sortByDate) ?? [],
    "comment"
  );

  const { data, handlePageChange, page, rowsPerPage, setPage, setRowsPerPage, total } =
    usePagination<Delta>(filteredItems);

  const { refetch: updatePolicyConfigs } = useQuery({
    queryKey: ["update-dps", vehicles.map((vehicle) => vehicle.identity.vin)],
    queryFn: () => updateDataProductPolicies(vehicles.map((vehicle) => vehicle.identity.vin)),
    enabled: false,
  });

  const { refetch: queryVehicles } = useQuery({
    queryKey: ["get-vehicles", vehicles.map((vehicle) => vehicle.identity.vin)],
    queryFn: () => getVehicles({ vins: vehicles.map((vehicle) => vehicle.identity.vin) }),
    enabled: false,
  });

  const applyDataProducts = useMutation({ mutationFn: applyDataProduct });

  const removeIndex = (index?: number) => {
    return setSendingRequest((prev) => {
      const updated = new Set(prev);
      updated.delete(index);
      return updated;
    });
  };

  const updatePolicies = async () => {
    const { data: policies } = await updatePolicyConfigs();
    if (policies?.status === 200) {
      const updatedActiveConfigs: DataProductPolicy[] = [];
      policies.data.data.forEach((element: DataProductPolicy) =>
        updatedActiveConfigs.push(element)
      );

      const { data: vehicles } = await queryVehicles();
      if (vehicles?.status === 200) setVehicles(vehicles.data.data);
    } else {
      notify({
        type: "error",
        title: "500 - Error occurred Getting Active Configs",
      });
    }
  };

  const handleSuccessNotifications = (
    results: DPResults[],
    requestType: string,
    dataProductId: string
  ) => {
    const successArray: string[] = [];
    const partialArray: string[] = [];
    results.forEach((result) => {
      if (result.status === "SUCCESS") {
        successArray.push(result.vin);
      } else {
        partialArray.push(result.vin + " returned " + result.status);
      }
    });
    if (successArray.length > 0) {
      notify({
        type: "success",
        title: `${dataProductId} has been ${
          requestType === "apply" ? "applied to " : "removed from "
        } VINs`,
        expandOption: true,
        expandContent: successArray,
      });
    }

    if (partialArray.length > 0) {
      notify({
        type: "warning",
        title: `Some vehicles were not processed - Data Products can't be ${
          requestType === "apply" ? "applied" : "removed"
        }`,
        expandOption: true,
        expandContent: partialArray,
      });
    }
  };

  const notifyUserOfRequests = async (
    dataProductId: string,
    requestArray: ApplyDataProduct[],
    warningArray: string[],
    requestType: "apply" | "remove"
  ) => {
    if (requestArray.length > 0) {
      const { data, status } = await applyDataProducts.mutateAsync(requestArray);
      if (status === 200) {
        handleSuccessNotifications(data.results, requestType, dataProductId);
      } else {
        notify({
          type: "error",
          title: `Error processing vehicle with data product ${dataProductId}`,
        });
      }
    }
    if (warningArray.length > 0) {
      notify({
        type: "warning",
        title: "Some VINs were not processed",
        expandOption: true,
        expandContent: warningArray,
      });
    }
  };

  const processApplyDPRequest = async (
    dataProductId: string,
    index: number,
    requestType: "apply" | "remove"
  ) => {
    if (vehicles.length === 0) {
      notify({ type: "warning", title: "Please Enter a VIN" });
      return;
    }

    try {
      setSendingRequest((prev) => new Set([...prev, index]));
      const requestArray: ApplyDataProduct[] = [];
      const warningArray: string[] = [];

      vehicles.forEach(({ identity, policies }) => {
        const configs =
          policies?.dataProduct?.pending?.configs || policies?.dataProduct?.active?.configs || [];
        let dataProductIds = configs.map((dpVersion) => dpVersion.dataProductId);

        if (requestType === "apply" && !dataProductIds.includes(dataProductId)) {
          dataProductIds.push(dataProductId);
        } else if (requestType === "remove" && dataProductIds.includes(dataProductId)) {
          dataProductIds = dataProductIds.filter((id) => id !== dataProductId);
        } else if (requestType === "remove") {
          warningArray.push(`VIN ${identity.vin} does not contain ${dataProductId}`);
          return;
        }

        requestArray.push({
          vin: identity.vin,
          dataProductIds: dataProductIds,
        });
      });

      notifyUserOfRequests(dataProductId, requestArray, warningArray, requestType);
    } catch {
      notify({
        type: "error",
        title: `Error processing vehicle with data product ${dataProductId}`,
      });
    }
    updatePolicies();
    removeIndex(index);
  };

  const handleInspectDP = (dataProduct: Delta) => {
    setModalData(dataProduct);
    inspectModal?.useModal?.show();
  };

  const getPermissions = () =>
    roles.some((role) => ["admin"].includes(role)) ||
    (roles.some((role) => ["tester"].includes(role)) && getEnvName() !== "Production");

  return (
    <>
      <InspectModal modalData={modalData} />
      <Card bodyStyles="text-center p-0 scroll-list" title="Data Products">
        {isLoadingDeltas ? (
          <Spinner />
        ) : (
          <table className="table table-bordered table-response" id="dp-dataProductsTable">
            <thead>
              <tr>
                <th aria-label="listheader">ID</th>
                <th aria-label="listheader">Modified</th>
                <th aria-label="listheader">Description</th>
                <th aria-label="listheader">Status</th>
                <th aria-label="listheader">Actions</th>
              </tr>
            </thead>
            <tbody className="align-middle">
              {data.map((dataProduct, index) => (
                <tr key={randomID()}>
                  <td>{dataProduct.dataProductId}</td>
                  <td>{getDateTime(dataProduct.modified)}</td>
                  <td>{dataProduct.comment}</td>
                  <td className="tableSymbol">
                    {getPermissions() ? (
                      <Popover
                        clickIcon={false}
                        label={
                          <StatusPopover
                            dataProductId={dataProduct.dataProductId}
                            refetch={queryDataProducts}
                            status={dataProduct.status}
                          />
                        }
                      >
                        <a className="errorSymbol">{dataProduct.status}</a>
                      </Popover>
                    ) : (
                      <div>{dataProduct.status}</div>
                    )}
                  </td>
                  <td className="d-grid gap-1 text-nowrap">
                    {sendingRequest.has(index) ? (
                      <Spinner />
                    ) : (
                      <>
                        <button
                          className="btn btn-primary"
                          id={"dp-dataProductsInspect" + index}
                          onClick={() => {
                            handleInspectDP(dataProduct);
                          }}
                        >
                          Inspect
                        </button>
                        <button
                          className="btn btn-success"
                          id={"dp-dataProductsSend" + index}
                          onClick={() =>
                            processApplyDPRequest(dataProduct.dataProductId, index, "apply")
                          }
                        >
                          Apply
                        </button>
                        <button
                          className="btn btn-danger"
                          id={"dp-dataProductsRemove" + index}
                          onClick={() =>
                            processApplyDPRequest(dataProduct.dataProductId, index, "remove")
                          }
                        >
                          Remove
                        </button>
                      </>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </Card>

      <SearchFilter searchFilter={searchFilter} />

      <Pagination
        handlePageChange={handlePageChange}
        page={page}
        rowsPerPage={rowsPerPage}
        setPage={setPage}
        setRowsPerPage={setRowsPerPage}
        total={total}
      />
    </>
  );
}
