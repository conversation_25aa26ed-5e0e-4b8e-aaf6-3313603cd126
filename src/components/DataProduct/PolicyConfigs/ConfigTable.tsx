/*
 * Copyright (c) Jaguar Land Rover Ltd 2022. All rights reserved
 */

import { DataProductPolicy } from "types/data";
import { randomID } from "utils/random";

interface PolicyConfigModalProps {
  policyData: DataProductPolicy;
}

const options: Intl.DateTimeFormatOptions = {
  year: "numeric",
  month: "long",
  day: "numeric",
  hour: "numeric",
  minute: "numeric",
  second: "numeric",
};

export function ConfigTable({ policyData }: PolicyConfigModalProps) {
  const renderVehicleInfo = () => {
    const activeConfigs = policyData.active?.configs || [];
    const pendingConfigs = policyData.pending?.configs || [];
    const fullConfigList = [
      ...activeConfigs.map((config) => ({ ...config, type: "Active" })),
      ...pendingConfigs.map((config) => ({ ...config, type: "Pending" })),
    ];

    if (fullConfigList.length === 0) {
      return (
        <tr>
          <td colSpan={3}>No Active Configs</td>
        </tr>
      );
    }

    return fullConfigList.map((config) => (
      <tr key={randomID()}>
        <td>{config.dataProductId ?? "null"}</td>
        <td>{config.type}</td>
        <td>
          {config.modified
            ? new Date(config.modified).toLocaleDateString(undefined, options)
            : "null"}
        </td>
      </tr>
    ));
  };

  return (
    <table className="table table-bordered text-center mb-0">
      <thead>
        <tr style={{ backgroundColor: "Gainsboro" }}>
          <th>Data Product ID</th>
          <th>Config Type</th>
          <th>Modified</th>
        </tr>
      </thead>
      <tbody>{renderVehicleInfo()}</tbody>
    </table>
  );
}
