/*
 * Copyright (c) Jaguar Land Rover Ltd 2022. All rights reserved
 */

import { FormEvent, useState } from "react";
import { useMutation } from "@tanstack/react-query";
import { useNotification } from "hooks/use-notification.hook";
import { Spinner } from "components/Spinner";
import { Card } from "components/Card";
import { Select } from "components/Form";
import { AutoStatusType } from "types/data";
import { makeVehicleAuto, makeVehicleNonAuto } from "api/data-product-delivery.api";

interface AutoStatusPopoverI {
  vin: string;
  autoStatus: AutoStatusType;
  refetch: () => void;
}

export const AutoStatusPopover = ({ vin, autoStatus, refetch }: AutoStatusPopoverI) => {
  const [updateAutoStatus, setUpdateAutoStatus] = useState<AutoStatusType>();
  const { notify } = useNotification();

  const disableStatusButton = () => {
    return !updateAutoStatus || autoStatus === updateAutoStatus || autoStatus === "Not Found";
  };

  const makeVehicleAutoRequest = useMutation({
    mutationFn: makeVehicleAuto,
    onSuccess: () => {
      notify({
        type: "success",
        title: `${vin} is included with auto DP processing `,
      });
      refetch();
    },
    onError: () => {
      notify({
        type: "warning",
        title: "Include vin for DP processing request encountered an error",
      });
    },
  });

  const makeVehicleNonAutoRequest = useMutation({
    mutationFn: makeVehicleNonAuto,
    onSuccess: () => {
      notify({
        type: "success",
        title: `${vin} is excluded from auto DP processing`,
      });
      refetch();
    },
    onError: () => {
      notify({
        type: "warning",
        title: "Exclude vin for auto DP processing request encountered an error",
      });
    },
  });

  const handleStatusUpdate = (ev: FormEvent) => {
    ev.preventDefault();
    if (autoStatus === "Auto") {
      makeVehicleNonAutoRequest.mutate(vin);
    } else {
      makeVehicleAutoRequest.mutate(vin);
    }
  };

  if (makeVehicleAutoRequest.isPending || makeVehicleNonAutoRequest.isPending) {
    return <Spinner />;
  }

  return (
    <Card cardStyles="" hasHeader={false}>
      <div className="d-flex flex-row align-items-end gap-3">
        <Select
          label="Update Auto Status"
          onChange={setUpdateAutoStatus}
          options={[
            { label: "Auto", value: "Auto" },
            { label: "Non-auto", value: "Non-Auto" },
          ]}
          value={updateAutoStatus}
        />
        <button
          className="btn btn-dark"
          disabled={disableStatusButton()}
          onClick={handleStatusUpdate}
          type="button"
        >
          Submit
        </button>
      </div>
    </Card>
  );
};
