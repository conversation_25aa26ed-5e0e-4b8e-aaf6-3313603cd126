/*
 * Copyright (c) Jaguar Land Rover Ltd 2022. All rights reserved
 */

import { useContext, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { DataProductPolicy, defaultPolicy } from "types/data";
import { Card } from "components/Card";
import { VehicleContext } from "context/vehicle.context";
import { Popover, Tooltip } from "components/Overlays";
import { randomID } from "utils/random";
import { getExcludedVins } from "api/data-product-delivery.api";
import { Bin, InfoCircle } from "components/Icons";
import { StatusIndicator } from "./StatusIndicator";
import { ConfigTable } from "./ConfigTable";
import { AutoStatusPopover } from "./AutoStatusPopover";

export function PolicyConfigs() {
  const { vehicles, setVehicles } = useContext(VehicleContext);

  const [policyData, setPolicyData] = useState<DataProductPolicy>(defaultPolicy);

  const {
    data: excludedVinResult,
    isLoading,
    refetch
  } = useQuery({
    queryKey: ["exclude-vins", vehicles],
    queryFn: () => getExcludedVins(vehicles.map((vehicle) => vehicle.identity.vin)),
    enabled: vehicles.length > 0
  });

  const handleRemove = (vin: string) => {
    setVehicles(vehicles.filter((item) => item.identity.vin !== vin));
  };

  const renderPolicyConfigsBtn = (policy?: DataProductPolicy) => {
    if (!policy || (!policy?.active && !policy?.pending)) {
      return <div className="text-secondary">No Policy Configs</div>;
    }
    return (
      <Popover clickIcon={false} label={<ConfigTable policyData={policyData} />}>
        <button className="btn btn-light border border-dark" onClick={() => setPolicyData(policy)}>
          See Details
        </button>
      </Popover>
    );
  };

  const getAutoStatusValue = (vin: string) => {
    if (!excludedVinResult?.data?.data) {
      return "Not Found";
    }
    const vinItem = excludedVinResult?.data?.data.find((item) => item.vin === vin);
    return vinItem?.isExcluded ? "Non-Auto" : "Auto";
  };

  const displayTableBody = () => {
    if (vehicles?.length === 0) {
      return (
        <tr>
          <td colSpan={4}>No VINS Added</td>
        </tr>
      );
    } else {
      return vehicles.map(({ identity, applications, policies, assets }, index) => {
        return (
          <tr className="align-middle" key={randomID()}>
            <td id={"vehiclesToUpdateStatus" + index}>
              <StatusIndicator
                manifest={applications?.vehicleAnalytics?.manifest}
                uniqueId={identity.uniqueId}
              />
            </td>
            <td id={"vehiclesToUpdateVin" + index}>
              <div className="vin-cell">
                <p>{identity.vin}</p>
                <div
                  className={`obg-pill ${
                    applications?.obg?.isEnabled ? "text-success" : "text-danger"
                  }`}
                >
                  {applications?.obg?.isEnabled ? "OBG" : "OBA"}
                </div>
              </div>
            </td>
            <td className="tableSymbol" id={"dp-vehiclesPermitted" + index}>
              <div>
                <Popover
                  clickIcon={false}
                  label={
                    <AutoStatusPopover
                      autoStatus={getAutoStatusValue(identity.vin)}
                      refetch={refetch}
                      vin={identity.vin}
                    />
                  }
                >
                  <a className="errorSymbol">
                    {isLoading ? "Loading" : getAutoStatusValue(identity.vin)}
                  </a>
                </Popover>
              </div>
            </td>
            <td className="tableSymbol" id={"dp-vehiclesToUpdateActiveConfigs" + index}>
              {renderPolicyConfigsBtn(policies?.dataProduct)}
            </td>
            <td>
              {assets?.vehicleArchitecture}
            </td>
            <td>
              <button
                aria-label="removeActiveConfigBtn"
                className="btn-close"
                id={"dp-vehiclesToUpdateRemove" + index}
                onClick={() => handleRemove(identity.vin)}
                role="button"
              />
            </td>
          </tr>
        );
      });
    }
  };

  return (
    <Card hasBody={false} title="Vehicles to Update">
      <table className="table table-bordered mb-0 text-center" id="dp-vehiclesToUpdateTable">
        <thead>
          <tr>
            <th aria-label="modifiedheader">Status</th>
            <th aria-label="modifiedheader">VIN</th>

            <th aria-label="modifiedheader">
              <div className="d-flex justify-content-center gap-2">
                Automation Status
                <Tooltip
                  label={
                    <div>
                      <p>
                        Displays whether the Data Products are automatically applied to these
                        vehicles or not
                      </p>
                      <ul>
                        <li>
                          <b>Auto: </b> DPs are automatically applied. You can't manually send a DP
                          to this vehicle
                        </li>
                        <li>
                          <b>Non-Auto: </b> DPs are not automatically applied. You can manually send
                          a DP to this vehicle
                        </li>
                      </ul>
                    </div>
                  }
                >
                  <InfoCircle color="#343434" />
                </Tooltip>
              </div>
            </th>

            <th aria-label="modifiedheader">Policy Configs</th>
            <th aria-label="modifiedheader">Vehicle Architecture</th>
            <th>
              <Bin />
            </th>
          </tr>
        </thead>
        <tbody>{displayTableBody()}</tbody>
      </table>
    </Card>
  );
}
