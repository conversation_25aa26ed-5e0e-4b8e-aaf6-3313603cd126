/*
 * Copyright (c) Jaguar Land Rover Ltd 2022. All rights reserved
 */

import { useCallback, useEffect, useState } from "react";
import { CheckIcon, ErrorCircleIcon, WarningTriangleIcon } from "components/Icons";
import { Manifest } from "types/data";
import { Tooltip } from "components/Overlays";

interface StatusProps {
  manifest?: Manifest;
  uniqueId: string;
}

const defaultStatus = {
  warning: false,
  error: false,
  allOk: false,
};

export function StatusIndicator({ manifest, uniqueId }: StatusProps) {
  const [title, setTitle] = useState("");
  const [indicator, setIndicator] = useState(defaultStatus);

  const getVinData = useCallback(async () => {
    const manifestVals = [
      manifest?.diagnosticsDictionary,
      manifest?.protocolChannels,
      manifest?.queries,
      manifest?.signalDictionary,
    ];
    // All values are "0.0"
    if (manifestVals.every((value) => value === "0.0")) {
      setTitle(
        `Vehicle - ${uniqueId} has not been initialised with default configuration. Provision the vehicle before applying data products`
      );
      setIndicator((prevState) => ({ ...prevState, warning: true }));
    }
    // All values are null or undefined
    else if (manifestVals.every((value) => !value)) {
      setTitle(`Vehicle - ${uniqueId} has never connected to VCDP cloud`);
      setIndicator((prevState) => ({ ...prevState, error: true }));
    } else {
      setTitle("Vehicle is ready for applying data products");
      setIndicator((prevState) => ({ ...prevState, allOk: true }));
    }
  }, [manifest, setTitle, uniqueId]);

  useEffect(() => {
    setIndicator(defaultStatus);
    getVinData();
  }, [getVinData]);

  let icon;
  if (indicator.warning) {
    icon = <WarningTriangleIcon />;
  } else if (indicator.error) {
    icon = <ErrorCircleIcon />;
  } else {
    icon = <CheckIcon />;
  }

  return (
    <Tooltip label={title}>
      <div data-testid="status-indicator-tooltip">{icon}</div>
    </Tooltip>
  );
}
