/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */
import { Key, useState } from "react";
import { Card } from "components/Card";
import { Inventory } from "types/data";
import { DownArrow, RightArrow } from "components/Icons";
import { DataKeysProp, displayValue, getNestedValue } from "./VehicleData";

const ecuDataKeys: DataKeysProp<Inventory>[] = [
  { name: "ECU Acronym", key: "ecuAcronym" },
  { name: "Diagnostic Variant", key: "diagnosticVariant" },
  { name: "Effective From", key: "effectiveFrom", format: "date" },
  { name: "Last Read", key: "lastRead", format: "date" },
  { name: "Serial Number", key: "modifiedBy" },
  { name: "DV Supported Onboard", key: "dvSupportedOnboard", format: "bool" },
  { name: "ECU Node Address", key: "hardwareInventory.ecuNodeAddr" },
  { name: "ECU Hardware Part Number", key: "hardwareInventory.ecuHwPartNo" },
  { name: "ECU Serial Number", key: "hardwareInventory.ecuSerialNumber" },
];

export const EcuDisplay = (inventory?: Inventory[]) => {
  const [expanded, setExpanded] = useState(false);

  if (!inventory?.length) {
    return (
      <Card isGrey title="ECU Components">
        No ECU Components Found
      </Card>
    );
  }

  return (
    <div>
      <button className="cursor-pointer reset-button-style" onClick={() => setExpanded(!expanded)}>
        <Card
          hasBody={false}
          title={
            <div>
              {expanded ? (
                <DownArrow color="white" size={16} />
              ) : (
                <RightArrow color="white" size={18} />
              )}
              <span style={{ marginLeft: "0.5rem" }}>
                {<b>ECU Components: {inventory.length}</b>}
              </span>
            </div>
          }
        />
      </button>

      {expanded && (
        <div className="mt-2 scroll-list">
          {inventory.map((module, index) => (
            <Card
              animate
              hasBody={false}
              isGrey
              key={module.ecuAcronym}
              title={<b>{`ECU Item #${index + 1}`}</b>}
            >
              <ul className="list-group">
                {ecuDataKeys.map(({ name, key, format }) => {
                  const keyVal = JSON.stringify(key);
                  const value = keyVal.includes(".")
                    ? getNestedValue(module, keyVal.replaceAll(/"/g, "").split("."))
                    : module[key as keyof unknown];
                  return (
                    <li className="list-group-item" key={key as Key}>
                      <strong>{name}: </strong>
                      {displayValue(value, format)}
                    </li>
                  );
                })}
              </ul>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};
