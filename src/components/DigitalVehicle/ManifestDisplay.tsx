/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

import { Key } from "react";
import { Card } from "components/Card";
import { Manifest } from "types/data";
import { DataKeysProp, displayValue } from "./VehicleData";

const manifestDataKeys: DataKeysProp<Manifest>[] = [
  { name: "VA Software Version", key: "vaAppSoftwareVersion" },
  { name: "Signal Dictionary", key: "signalDictionary" },
  { name: "Signal Dictionary Schema", key: "signalDictionarySchema" },
  { name: "Diagnostics Dictionary", key: "diagnosticsDictionary" },
  {
    name: "Diagnostics Dictionary Schema",
    key: "diagnosticsDictionarySchema",
  },
  { name: "Queries", key: "queries" },
  { name: "Queries Schema", key: "queriesSchema" },
  { name: "Protocol Channels", key: "protocolChannels" },
  { name: "Protocol Channels Schema", key: "protocolChannelsSchema" },
];

export const ManifestDisplay = (manifest?: Manifest) => {
  if (!manifest) {
    return (
      <Card isGrey title="Manifest">
        No Manifest Data Found
      </Card>
    );
  }
  return (
    <Card animate hasBody={false} title={<b>Manifest</b>}>
      <ul className="list-group">
        {manifestDataKeys.map(({ name, key, format }) => {
          const value = manifest[key as keyof unknown];
          return (
            <li className="list-group-item" key={key as Key}>
              <strong>{name}: </strong>
              {displayValue(value, format)}
            </li>
          );
        })}
      </ul>
    </Card>
  );
};
