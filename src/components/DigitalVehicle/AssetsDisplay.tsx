/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

import { Key } from "react";
import { Card } from "components/Card";
import { Assets } from "types/data";
import { DataKeysProp, displayValue } from "./VehicleData";

const assetDataKeys: DataKeysProp<Assets>[] = [
  { name: "Fleet ID", key: "fleetId" },
  { name: "Brand", key: "brand" },
  { name: "Model Range", key: "modelRange" },
  { name: "Target Market", key: "targetMarket" },
  { name: "Sold Into Market", key: "soldIntoMarket" },
  { name: "Model Year", key: "modelYear" },
  { name: "Model Name", key: "modelName" },
  { name: "Trim", key: "trim" },
  { name: "Body Style", key: "bodyStyle" },
  { name: "Driver", key: "driver" },
  { name: "Transmission", key: "transmission" },
  { name: "Engine", key: "engine" },
  { name: "Plant", key: "plant" },
  { name: "Fuel Type", key: "fuelType" },
  { name: "Vehicle Architecture", key: "vehicleArchitecture" },
];

export const AssetsDisplay = (assets?: Assets) => {
  if (!assets) {
    return (
      <Card isGrey title="Assets">
        No Assets Data Found
      </Card>
    );
  }
  return (
    <Card animate hasBody={false} title={<b>Assets</b>}>
      <ul className="list-group">
        {assetDataKeys.map(({ name, key, format }) => {
          const value = assets[key as keyof unknown];
          return (
            <li className="list-group-item" key={key as Key}>
              <strong>{name}: </strong>
              {displayValue(value, format)}
            </li>
          );
        })}
      </ul>
    </Card>
  );
};
