/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

import { Vehicle } from "types/data";
import { IdentityDisplay } from "./IdentityDisplay";
import { ControlDisplay } from "./ControlDisplay";
import { ManifestDisplay } from "./ManifestDisplay";
import { EcuDisplay } from "./EcuDisplay";
import { AssetsDisplay } from "./AssetsDisplay";
import { FeatureCodesDisplay } from "./FeatureCodesDisplay";

interface DvDataDisplayProps {
  dvData?: Vehicle;
  searchVehicle: () => void;
}

export const DvDataDisplay = ({ dvData, searchVehicle }: DvDataDisplayProps) => {
  return (
    <div className="row">
      <div className="col-lg-6 col-md-12">
        {IdentityDisplay(dvData?.identity, dvData?.applications?.obg)}
        {ControlDisplay(dvData?.applications?.vehicleAnalytics?.status)}
        {ManifestDisplay(dvData?.applications?.vehicleAnalytics?.manifest)}
      </div>
      <div className="col-lg-6 col-md-12">
        {AssetsDisplay(dvData?.assets)}
        {FeatureCodesDisplay(searchVehicle, dvData?.identity.vin, dvData?.assets.featureCodes)}
        {EcuDisplay(dvData?.inventory)}
      </div>
    </div>
  );
};
