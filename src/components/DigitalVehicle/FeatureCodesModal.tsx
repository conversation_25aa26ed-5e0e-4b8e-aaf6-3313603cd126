/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

import { useMemo, useState } from "react";
import { useMutation } from "@tanstack/react-query";
import { Modal } from "bootstrap";
import { patchVehicle } from "api/digital-vehicle.api";
import { useNotification } from "hooks/use-notification.hook";
import { PatchVehicle } from "types/patch-vehicle";

interface FeatureCodesModalProps {
  modalContent: FeatureCodesModalContent;
  searchVehicle: () => void;
}

export interface FeatureCodesModalContent {
  vin: string;
  availableCodes: string[];
  currentCodes: string[];
}

export const FeatureCodesModal = (props: FeatureCodesModalProps) => {
  const { modalContent } = props;
  const { notify } = useNotification();

  const patchRequest = useMutation({
    mutationFn: patchVehicle,
    onSuccess: () => {
      const modalElement = document.getElementById("codesModal")!;
      Modal.getInstance(modalElement)?.hide();
      props.searchVehicle();
      setAddCodes([]);
      setRemoveCodes([]);
      notify({
        type: "success",
        title: "Feature Codes Updated",
        body: "Digital Vehicle details have been refreshed",
      });
    },
    onError: (error) => {
      notify({
        type: "error",
        title: "Error updating feature codes",
        body: error.message,
      });
    },
  });

  const [availableSearch, setAvailableSearch] = useState("");
  const [currentSearch, setCurrentSearch] = useState("");

  const [addCodes, setAddCodes] = useState<string[]>([]);
  const [removeCodes, setRemoveCodes] = useState<string[]>([]);

  const filteredAvailableCodes = useMemo(() => {
    return modalContent.availableCodes
      .filter((code) => !modalContent.currentCodes.includes(code))
      .filter((code) => code.toLowerCase().includes(availableSearch.toLowerCase()));
  }, [modalContent.availableCodes, modalContent.currentCodes, availableSearch]);

  const filteredCurrentCodes = useMemo(() => {
    return modalContent.currentCodes.filter((code) =>
      code.toLowerCase().includes(currentSearch.toLowerCase())
    );
  }, [modalContent.currentCodes, currentSearch]);

  const toggleAddCodeSelection = (code: string) => {
    setAddCodes((prev) =>
      prev.includes(code) ? prev.filter((prevCode) => prevCode !== code) : [...prev, code]
    );
  };

  const toggleRemoveCodeSelection = (code: string) => {
    setRemoveCodes((prev) =>
      prev.includes(code) ? prev.filter((prevCode) => prevCode !== code) : [...prev, code]
    );
  };

  const handleSave = () => {
    const updatedCodes = [
      ...modalContent.currentCodes.filter((code) => !removeCodes.includes(code)),
      ...addCodes,
    ];
    const updatedVehicle: PatchVehicle = { featureCodes: updatedCodes };
    patchRequest.mutate({
      patchVehicleRequest: updatedVehicle,
      vin: modalContent.vin,
    });
  };

  return (
    <div
      aria-hidden="true"
      aria-labelledby="featureCodesModalLabel"
      className="modal fade"
      id="codesModal"
      tabIndex={-1}
    >
      <div className="modal-dialog modal-lg" role="document">
        <div className="modal-content">
          <div className="modal-header">
            <h5 className="modal-title" id="contained-modal-title-vcenter">
              Update Feature Codes for {modalContent.vin}
            </h5>
          </div>
          <div className="modal-body">
            <div className="row mb-5 mt-2">
              <div className="col-md-6">
                <div className="mb-2">
                  <div className="input-group input-group-sm" style={{ maxWidth: "250px" }}>
                    <input
                      className="form-control"
                      onChange={(event) => setCurrentSearch(event.target.value)}
                      placeholder="Search Current..."
                      type="text"
                      value={currentSearch}
                    />
                    {currentSearch && (
                      <button
                        className="btn btn-outline-secondary btn-sm"
                        onClick={() => setCurrentSearch("")}
                        type="button"
                      >
                        ×
                      </button>
                    )}
                  </div>
                </div>
                <div className="border rounded" style={{ height: "300px", overflowY: "auto" }}>
                  <ul className="list-group list-group-flush">
                    {filteredCurrentCodes.length === 0 ? (
                      <li className="list-group-item list-group-item-secondary">
                        No current feature codes {currentSearch ? "match your search" : "found"}
                      </li>
                    ) : (
                      <>
                        <li
                          className="list-group-item list-group-item-secondary"
                          style={{
                            position: "sticky",
                            top: 0,
                            zIndex: 1,
                          }}
                        >
                          Current Codes ({filteredCurrentCodes.length})
                        </li>
                        {filteredCurrentCodes.map((code) => (
                          <li
                            className={`list-group-item list-group-item-action ${
                              removeCodes.includes(code) ? "list-group-item-danger" : ""
                            }`}
                            key={code}
                            onClick={() => toggleRemoveCodeSelection(code)}
                            onKeyDown={(event) => {
                              if (event.key === "Enter") {
                                event.preventDefault();
                                toggleRemoveCodeSelection(code);
                              }
                            }}
                            style={{ cursor: "pointer" }}
                            tabIndex={0}
                          >
                            {code}
                          </li>
                        ))}
                      </>
                    )}
                  </ul>
                </div>
              </div>

              <div className="col-md-6">
                <div className="d-flex justify-content-between align-items-center mb-2">
                  <div className="input-group" style={{ maxWidth: "250px" }}>
                    <input
                      className="form-control form-control-sm"
                      onChange={(event) => setAvailableSearch(event.target.value)}
                      placeholder="Search Available..."
                      type="text"
                      value={availableSearch}
                    />
                    {availableSearch && (
                      <button
                        className="btn btn-outline-secondary btn-sm"
                        onClick={() => setAvailableSearch("")}
                        type="button"
                      >
                        ×
                      </button>
                    )}
                  </div>
                  {(addCodes.length > 0 || removeCodes.length > 0) && (
                    <button
                      className="btn btn-outline-danger btn-sm"
                      onClick={() => {
                        setAddCodes([]);
                        setRemoveCodes([]);
                      }}
                    >
                      Clear Selection
                    </button>
                  )}
                </div>
                <div className="border rounded" style={{ height: "300px", overflowY: "auto" }}>
                  <ul className="list-group list-group-flush">
                    {filteredAvailableCodes.length === 0 ? (
                      <li className="list-group-item list-group-item-secondary">
                        No available feature codes {availableSearch ? "match your search" : "found"}
                      </li>
                    ) : (
                      <>
                        <li
                          className="list-group-item list-group-item-secondary"
                          style={{
                            position: "sticky",
                            top: 0,
                            zIndex: 1,
                          }}
                        >
                          Available Codes ({filteredAvailableCodes.length})
                        </li>
                        {filteredAvailableCodes.map((code) => (
                          <li
                            className={`list-group-item list-group-item-action ${
                              addCodes.includes(code) ? "list-group-item-success" : ""
                            }`}
                            key={code}
                            onClick={() => toggleAddCodeSelection(code)}
                            onKeyDown={(event) => {
                              if (event.key === "Enter") {
                                event.preventDefault();
                                toggleAddCodeSelection(code);
                              }
                            }}
                            style={{ cursor: "pointer" }}
                            tabIndex={0}
                          >
                            {code}
                          </li>
                        ))}
                      </>
                    )}
                  </ul>
                </div>
              </div>
            </div>

            {(addCodes.length > 0 || removeCodes.length > 0) && (
              <div className="alert alert-info d-flex flex-column" role="alert">
                {removeCodes.length > 0 && (
                  <div className={`small ${addCodes.length > 0 && "mb-2"}`}>
                    <b>Remove:</b> {removeCodes.join(", ")}
                  </div>
                )}
                {addCodes.length > 0 && (
                  <div className="small">
                    <b>Add:</b> {addCodes.join(", ")}
                  </div>
                )}
              </div>
            )}
          </div>
          <div className="modal-footer">
            <button
              className="btn btn-light"
              data-bs-dismiss="modal"
              onClick={() => {
                setAddCodes([]);
                setRemoveCodes([]);
              }}
              type="button"
            >
              Cancel
            </button>
            <button
              className="btn btn-dark"
              onClick={handleSave}
              disabled={addCodes.length === 0 && removeCodes.length === 0}
              type="button"
            >
              Save Changes
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
