/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Card } from "components/Card";
import { useModalHandler } from "hooks/use-modal-handler.hook";
import { getFeatureCodes } from "api/digital-vehicle.api";
import { FeatureCodesModal, FeatureCodesModalContent } from "./FeatureCodesModal";

export const FeatureCodesDisplay = (
  searchVehicle: () => void,
  vin?: string,
  currentCodes?: string[]
) => {
  const { useModal } = useModalHandler("codesModal");
  const [modalContent, setModalContent] = useState<FeatureCodesModalContent>({
    vin: "",
    availableCodes: [],
    currentCodes: [],
  });

  const { data: availableCodes } = useQuery({
    queryKey: ["codes"],
    queryFn: () => getFeatureCodes(),
  });

  const handleEdit = () => {
    setModalContent({
      vin: vin!,
      availableCodes: availableCodes?.featureCodes || [],
      currentCodes: currentCodes || [],
    });
    useModal?.show();
  };

  if (!currentCodes?.length) {
    return (
      <>
        <FeatureCodesModal modalContent={modalContent} searchVehicle={searchVehicle} />
        <Card hasButton={vin ? { onClick: handleEdit } : undefined} isGrey title="Feature Codes">
          No Feature Codes Found
        </Card>
      </>
    );
  }

  return (
    <>
      <FeatureCodesModal modalContent={modalContent} searchVehicle={searchVehicle} />
      <Card
        animate
        bodyStyles="scroll-feature-code"
        hasBody={false}
        hasButton={{ onClick: handleEdit }}
        title={<b>Feature Codes</b>}
      >
        <ul className="list-group">
          {currentCodes.map((code, index) => {
            return (
              <li className="list-group-item" key={index}>
                {code}
              </li>
            );
          })}
        </ul>
      </Card>
    </>
  );
};
