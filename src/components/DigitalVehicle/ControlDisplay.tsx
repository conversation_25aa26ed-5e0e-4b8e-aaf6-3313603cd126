/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

import { Key } from "react";
import { Card } from "components/Card";
import { Status } from "types/data";
import { DataKeysProp, displayValue } from "./VehicleData";

const controlDataKeys: DataKeysProp<Status>[] = [
  { name: "Enable VA", key: "enableVA", format: "bool" },
  { name: "Allow WiFi", key: "allowWiFi", format: "bool" },
  { name: "Enable Mirroring", key: "enableMirroring", format: "bool" },
  { name: "Enable Push Data", key: "enablePushData", format: "bool" },
  { name: "Last VA Controls Update", key: "lastVaControlsUpdate", format: "date" },
];

export const ControlDisplay = (status?: Status) => {
  if (!status) {
    return (
      <Card isGrey title="Control">
        No Control Data Found
      </Card>
    );
  }
  return (
    <Card animate hasBody={false} title={<b>Control</b>}>
      <ul className="list-group">
        {controlDataKeys.map(({ name, key, format }) => {
          const value = status[key as keyof unknown];
          return (
            <li className="list-group-item" key={key as Key}>
              <strong>{name}: </strong>
              {displayValue(value, format)}
            </li>
          );
        })}
      </ul>
    </Card>
  );
};
