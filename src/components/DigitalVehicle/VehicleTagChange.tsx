/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

import { FormEvent, useState } from "react";
import { useMutation } from "@tanstack/react-query";
import { usePermissions } from "hooks/use-permissions";
import { addVehicleTags, deleteVehicleTags } from "../../api/digital-vehicle.api";
import { useNotification } from "../../hooks/use-notification.hook";
import { Spinner } from "../Spinner";
import { isArrayEqual } from "../../utils/array-utils";

interface VehicleTagChangeI {
  vin: string;
  searchVehicle: (ev: FormEvent) => void;
  distinctTagList: string[];
  originalActiveTags: string[];
}

export const VehicleTagChange = ({
  vin,
  searchVehicle,
  distinctTagList,
  originalActiveTags,
}: VehicleTagChangeI) => {
  const { hasPermission } = usePermissions();
  const { notify } = useNotification();
  const addTagRequest = useMutation({ mutationFn: addVehicleTags });
  const deleteTagRequest = useMutation({ mutationFn: deleteVehicleTags });

  const originalAvailableTags = distinctTagList.filter(
    (value) => !originalActiveTags.includes(value)
  );
  const [currentAvailableTags, setCurrentAvailableTags] = useState<string[]>(originalAvailableTags);
  const [currentActiveTags, setCurrentActiveTags] = useState<string[]>(originalActiveTags);

  // This logic is to suit the current version of the DVSH tag-controller endpoints, which currently accept one tag.
  const handleVehicleTags = async (ev: FormEvent) => {
    const tagsForAdding = currentActiveTags.filter((value) => !originalActiveTags.includes(value));
    const tagsForRemoval = originalActiveTags.filter((value) => !currentActiveTags.includes(value));

    for (const tag of tagsForAdding) {
      const result = await addTagRequest.mutateAsync({ vins: [vin], tag: tag });
      if (result.status === 200) {
        notify({
          type: "success",
          title: `Successfully added tag ${tag} to vehicle ${vin}`,
        });
      } else if (addTagRequest.isError) {
        notify({
          type: "error",
          title: "Vehicle tag addition application encountered an error",
        });
      }
    }
    for (const tag of tagsForRemoval) {
      const result = await deleteTagRequest.mutateAsync({ vins: [vin], tag: tag });
      if (result.status === 200) {
        notify({
          type: "success",
          title: `Successfully removed tag ${tag} from vehicle ${vin}`,
        });
      } else if (addTagRequest.isError) {
        notify({
          type: "error",
          title: "Vehicle tag deletion application encountered an error",
        });
      }
    }

    searchVehicle(ev);
  };

  const addTagToActive = (item: string) => {
    setCurrentAvailableTags(currentAvailableTags.filter((tag) => tag !== item));
    setCurrentActiveTags([...currentActiveTags, item]);
  };

  const removeTagFromActive = (item: string) => {
    setCurrentActiveTags(currentActiveTags.filter((tag) => tag !== item));
    setCurrentAvailableTags([...currentAvailableTags, item]);
  };

  const handleResetVehicleTags = () => {
    setCurrentAvailableTags(originalAvailableTags);
    setCurrentActiveTags(originalActiveTags);
  };

  const disableButton = () => {
    return isArrayEqual(originalActiveTags, currentActiveTags);
  };

  if (addTagRequest.isPending || deleteTagRequest.isPending) {
    return <Spinner />;
  }
  if (hasPermission("VehicleTags")) {
    return (
      <div>
        <p>Add or Remove Vehicle Tags To Active Group</p>
        <div className="d-flex flex-row gap-3">
          <div
            className="d-flex flex-column align-items-center gap-1 w-50"
            data-testid="AvailableTagDiv"
          >
            <p>
              <strong>Available Tags</strong>
            </p>
            {currentAvailableTags.length === 0 ? (
              <p>No Tags Available</p>
            ) : (
              currentAvailableTags.map((item, index) => (
                <button
                  className="btn btn-outline-dark"
                  key={index}
                  onClick={() => addTagToActive(item)}
                >
                  {item}
                </button>
              ))
            )}
          </div>
          <div
            className="d-flex flex-column align-items-center gap-1 w-50"
            data-testid="ActiveTagDiv"
          >
            <p>
              <strong>Active Tags</strong>
            </p>
            {currentActiveTags.length === 0 ? (
              <p>No Active Tags</p>
            ) : (
              currentActiveTags.map((item, index) => (
                <button
                  className="btn btn-outline-success"
                  key={index}
                  onClick={() => removeTagFromActive(item)}
                >
                  {item}
                </button>
              ))
            )}
          </div>
        </div>
        <div className="d-flex justify-content-end gap-2 pt-4">
          <button
            className="btn btn-light border-dark"
            disabled={disableButton()}
            onClick={handleResetVehicleTags}
            type="button"
          >
            Reset
          </button>
          <button
            className="btn btn-dark"
            disabled={disableButton()}
            onClick={handleVehicleTags}
            type="button"
          >
            Update
          </button>
        </div>
      </div>
    );
  }
};
