/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

import { Paths } from "types/paths";
import { getDateTime } from "utils/dates";

export interface DataKeysProp<T> {
  name: string;
  key: Paths<T>;
  format?: string;
}

const booleanToText = (value: boolean) => (value ? "TRUE" : "FALSE");

const obgEnabled = (value: boolean) => (value ? "OBG" : "OBA");

export const getNestedValue = <T,>(val: T, lst: string[]): unknown => {
  if (lst.length != 0) {
    const firstItem = lst.shift();
    return firstItem ? getNestedValue(val[firstItem as keyof unknown], lst) : val;
  } else {
    return val;
  }
};

export const displayValue = (value: unknown, format?: string) => {
  if (format == "bool") return booleanToText(value as boolean);
  if (format == "date" && value) return getDateTime(value as string);
  if (format == "obg") return obgEnabled(value as boolean);
  if (!value || value === "") return <span className="na">N/A</span>;
  return value as string;
};
