/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

import { FormEvent, useContext, useState } from "react";
import { useMutation } from "@tanstack/react-query";
import { Spinner } from "components/Spinner";
import { AuthContext } from "context/auth.context";
import { getEnvName } from "utils/constants";
import { useNotification } from "hooks/use-notification.hook";
import { Select } from "components/Form";
import { Obg } from "types/data";
import { updateObgApplication } from "api/digital-vehicle.api";

interface ObgChangeI {
  searchVehicle: (ev: FormEvent) => void;
  vehicleArchitecture?: string,
  uniqueId?: string;
  obg?: Obg;
}

export const ObgChange = ({ obg, vehicleArchitecture, uniqueId, searchVehicle }: ObgChangeI) => {
  const { roles } = useContext(AuthContext);
  const { notify } = useNotification();
  const obgRequest = useMutation({ mutationFn: updateObgApplication });
  const [isEnabled, setIsEnabled] = useState<string>();

  const handleObgUpdate = async (ev: FormEvent) => {
    if (isEnabled !== null && uniqueId) {
      const result = await obgRequest.mutateAsync({
        uniqueId,
        isEnabled: isEnabled === "true",
      });
      if (result.status === 200) {
        notify({
          type: "success",
          title: `Vehicle type updated to ${isEnabled === "true" ? "OBG" : "OBA"}`,
        });
        searchVehicle(ev);
      } else if (obgRequest.isError) {
        notify({
          type: "error",
          title: "OBG application update encountered an error",
        });
      }
    }
  };

  const getPermissions = () => {
    return (
      roles.some((role) => ["admin", "tester"].includes(role)) && getEnvName() !== "Production"
    );
  };

  const disableObgUpdateButton = () => {
    return !isEnabled || vehicleArchitecture === "EVA_25" || obg?.isEnabled === (isEnabled == "true");
  };

  if (obgRequest.isPending) {
    return <Spinner />;
  }
  if (getPermissions()) {
    return (
      <div>
        <p>Change Vehicle Type to OBG or OBA</p>
        <div className="d-flex flex-row align-items-center gap-3">
          <div className="flex-grow-1">
            <Select
              onChange={setIsEnabled}
              options={[
                { label: "OBG", value: "true" },
                { label: "OBA", value: "false" },
              ]}
              value={isEnabled}
            />
          </div>
          <button
            className="btn btn-dark"
            disabled={disableObgUpdateButton()}
            onClick={handleObgUpdate}
            type="button"
          >
            Submit
          </button>
        </div>
      </div>
    );
  }
};
