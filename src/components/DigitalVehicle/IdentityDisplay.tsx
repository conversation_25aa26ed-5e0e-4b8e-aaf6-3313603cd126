/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

import { Key } from "react";
import isEmpty from "lodash/isEmpty";
import { Card } from "components/Card";
import { Identity, Obg } from "types/data";
import { DataKeysProp, displayValue } from "./VehicleData";

const vehicleDetailKeys: DataKeysProp<Identity & Obg>[] = [
  { name: "VIN", key: "vin" },
  { name: "Vehicle Type", key: "isEnabled", format: "obg" },
  { name: "Unique ID", key: "uniqueId" },
  { name: "Identity Created", key: "identityCreated", format: "date" },
  { name: "Vehicle Reg", key: "vehicleReg" },
  { name: "Squish VIN", key: "squishVin" },
];

export const IdentityDisplay = (identity?: Identity, obg?: Obg) => {
  const display = { ...identity, ...obg };

  if (isEmpty(display)) {
    return (
      <Card isGrey title="Identity">
        No Identity Data Found
      </Card>
    );
  }

  return (
    <Card animate hasBody={false} title={<b>Identity</b>}>
      <ul className="list-group">
        {vehicleDetailKeys.map(({ name, key, format }) => {
          const value = display[key as keyof unknown];
          return (
            <li className="list-group-item" key={key as Key}>
              <strong>{name}: </strong>
              {displayValue(value, format)}
            </li>
          );
        })}
      </ul>
    </Card>
  );
};
