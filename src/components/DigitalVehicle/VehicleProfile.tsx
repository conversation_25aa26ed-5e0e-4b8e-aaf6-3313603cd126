/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

import { useState } from "react";
import { useMutation } from "@tanstack/react-query";
import { Spinner } from "components/Spinner";
import { useNotification } from "hooks/use-notification.hook";
import { Select } from "components/Form";
import { patchVehicle } from "api/digital-vehicle.api";
import { PatchVehicle } from "types/patch-vehicle";
import { profileTemplates } from "utils/profile-templates";

interface VehicleProfileProps {
  searchVehicle: () => void;
  vin: string;
}

export const VehicleProfile = ({ searchVehicle, vin }: VehicleProfileProps) => {
  const { notify } = useNotification();
  const profileRequest = useMutation({
    mutationFn: patchVehicle,
    onSuccess: () => {
      searchVehicle();
      notify({
        type: "success",
        title: `${templateSelected} Profile Added`,
        body: "Digital Vehicle details have been refreshed",
      });
    },
    onError: (error) => {
      notify({
        type: "error",
        title: "Error updating vehicle profile",
        body: error.message,
      });
    },
  });

  const [templateSelected, setTemplateSelected] = useState<string>();

  const handleSubmit = () => {
    const selectedTemplate: PatchVehicle = profileTemplates[templateSelected!];

    profileRequest.mutate({
      patchVehicleRequest: selectedTemplate,
      vin: vin,
    });
  };

  if (profileRequest.isPending) {
    return <Spinner />;
  }
  return (
    <div>
      <p>Update the vehicle to a predefined template</p>
      <div className="d-flex flex-row align-items-center gap-3">
        <div className="flex-grow-1">
          <Select
            onChange={setTemplateSelected}
            options={[{ label: "L460", value: "L460" }]}
            value={templateSelected}
          />
        </div>
        <button
          className="btn btn-dark"
          disabled={!templateSelected}
          onClick={handleSubmit}
          type="button"
        >
          Submit
        </button>
      </div>
    </div>
  );
};
