import { useMutation } from "@tanstack/react-query";
import { ChangeEvent, FormEvent, useState } from "react";
import { getVehicles, postTestRig } from "api/digital-vehicle.api";
import { Card } from "components/Card";
import { useNotification } from "hooks/use-notification.hook";
import { TestRigRequest } from "types/data";
import { formatValidation } from "utils/format-validation";

interface CreateRigProps {
  createRigLoading: boolean;
  setCreateRigLoading: (val: boolean) => void;
  setRigVin: (val: string) => void;
  setShouldPoll: (val: boolean) => void;
  shouldPoll: boolean;
}

export function CreateRig({
  createRigLoading,
  setCreateRigLoading,
  setRigVin,
  setShouldPoll,
  shouldPoll,
}: CreateRigProps) {
  const [formData, setFormData] = useState<TestRigRequest>({
    vin: "",
    tcu: "",
    cert: "",
  });

  const fetchVehicle = useMutation({ mutationFn: getVehicles });
  const createTestRig = useMutation({
    mutationFn: postTestRig,
    onMutate: () => setCreateRigLoading(true),
    onSuccess: () => {
      setShouldPoll(true);
      setCreateRigLoading(false);
      notify({
        type: "success",
        title: "Test Rig Created",
        body: "Rig state is being refreshed. Please wait",
      });
    },
    onError: (error) => {
      notify({
        type: "error",
        title: "Error Creating Test Rig",
        body: error.message,
      });
    },
  });

  const { notify } = useNotification();

  const isButtonDisabled = formData.vin && formData.tcu && formData.cert;

  const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    // Validate the VIN
    const { formattedVins } = formatValidation([formData.vin]);
    const vin = formattedVins[0];

    if (!vin) {
      notify({
        type: "error",
        title: `${formData.vin} is not a valid VIN. Must be alphanumerical and have 17 characters.`,
      });
      return;
    }

    // Check if the VIN already exists
    const { data, status } = await fetchVehicle.mutateAsync({
      vins: [vin],
      inclusions: ["IDENTITY"],
    });

    if (status === 200 && data.data.length > 0) {
      notify({
        type: "error",
        title: `Vehicle with VIN ${vin} already exists.`,
        body: "Please choose another VIN.",
      });
      return;
    }

    // Create the rig
    createTestRig.mutate(formData);
  };

  const handleChange = (event: ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = event.target;
    setFormData({ ...formData, [name]: value });
    if (name === "vin") {
      setRigVin(value);
    }
  };

  return (
    <Card title="Add a Rig">
      <form className="d-flex flex-column my-3 gap-3" onSubmit={handleSubmit}>
        <div className="form-group">
          <label className="boldLabel">VIN</label>
          <input
            className="form-control"
            name="vin"
            onChange={handleChange}
            type="text"
            value={formData.vin}
          />
        </div>
        <div className="form-group">
          <label className="boldLabel">TCU Serial Number</label>
          <input
            className="form-control"
            name="tcu"
            onChange={handleChange}
            type="text"
            value={formData.tcu}
          />
        </div>
        <div className="form-group">
          <label className="boldLabel">Cert Type</label>
          <select className="form-select" name="cert" onChange={handleChange} value={formData.cert}>
            <option disabled value="">
              Select an option
            </option>
            <option value="ENG">Engineering</option>
            <option value="PROD">Production</option>
          </select>
        </div>
        <div className="text-center">
          <button
            className="btn btn-dark mt-3"
            disabled={!isButtonDisabled || createRigLoading || shouldPoll}
            type="submit"
          >
            {createRigLoading || shouldPoll ? "Processing..." : "Create Rig"}
          </button>
        </div>
      </form>
    </Card>
  );
}
