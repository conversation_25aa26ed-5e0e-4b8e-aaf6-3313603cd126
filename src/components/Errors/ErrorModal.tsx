/*
 * Copyright (c) Jaguar Land Rover Ltd 2022. All rights reserved
 */

import { ErrorMessage } from "types/data";
import { getDateTime } from "utils/dates";
import { randomID } from "utils/random";

interface ErrorModalProps {
  shouldUseFilter?: boolean;
  errorMessages: ErrorMessage[];
}

export function ErrorModal({ shouldUseFilter, errorMessages }: ErrorModalProps) {
  const faultIdentifiers = ["0x10", "0x11", "0x12", "0x20"];

  const displayErrorBody = () => {
    if (!errorMessages || errorMessages?.length === 0) {
      return (
        <tr className="text-center">
          <td colSpan={4}>All Good With This Vehicle</td>
        </tr>
      );
    }

    const filteredMessages = shouldUseFilter
      ? errorMessages.filter((errorMsg) => faultIdentifiers.includes(errorMsg.faultIdentifier))
      : errorMessages;

    return filteredMessages.map((error: ErrorMessage) => (
      <tr className="text-center trBorder" key={randomID()}>
        <td className="tdBorder">{error.faultIdentifier}</td>
        <td className="tdBorder">{error.errorMetadata}</td>
        <td className="tdBorder">{error.severity}</td>
        <td className="tdBorder">{getDateTime(Number(error.timestamp))}</td>
      </tr>
    ));
  };

  return (
    <table aria-label="errorTable" className="table table-bordered m-0">
      <thead>
        <tr className="trBorder">
          <th className="thBackground">Fault Identifier</th>
          <th className="thBackground">Description</th>
          <th className="thBackground">Severity</th>
          <th className="thBackground">Timestamp</th>
        </tr>
      </thead>
      <tbody className="error-tbody">{displayErrorBody()}</tbody>
    </table>
  );
}
