/*
 * Copyright (c) Jaguar Land Rover Ltd 2022. All rights reserved
 */

import { useNavigate } from "react-router-dom";

type ErrorProps = {
  error?: string;
};

export const ErrorPage = ({ error = "AN ERROR HAS OCCURRED." }: ErrorProps) => {
  const navigate = useNavigate();
  const returnHome = () => navigate("/");
  const reloadWindow = () => navigate(0);

  return (
    <div className="container-fluid">
      <div className="row">
        <div style={{ padding: "4rem" }}>
          <h1>OOPS! </h1>
          <p>Message: {error}</p>
          <hr className="my-2" />
          <div className="d-flex flex-row gap-3">
            <button className="btn btn-lg btn-outline-dark" onClick={returnHome} role="button">
              Return to Home Page
            </button>
            <button className="btn btn-lg btn-outline-dark" onClick={reloadWindow} role="button">
              Refresh Page
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
