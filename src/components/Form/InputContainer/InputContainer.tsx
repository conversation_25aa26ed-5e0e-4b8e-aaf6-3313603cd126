/*
 * Copyright 2022 (c) Jaguar Land Rover Ltd. All rights reserved.
 */

import { CSSProperties, ReactNode, useMemo } from "react";
import { FormComponentProps } from "types/form-component-props";
import { randomID } from "utils/random";
import styles from "./InputContainer.module.scss";

export interface InputContainerProps extends FormComponentProps {
  style?: CSSProperties;
  render: (ID: string) => ReactNode;
}

export const InputContainer = (props: InputContainerProps) => {
  const { label, description, error, style = {}, isRequired = false, render } = props;
  const id = useMemo(randomID, []);

  return (
      <div className={styles.container} style={style}>
        {label && (
            <label htmlFor={id}>
              {label}
              {isRequired && <span className={styles.isRequired}>*</span>}
              {description && <div className="description">{description}</div>}
            </label>
        )}
        {render(id)}
        {error && <div className={styles.isRequired}>{error}</div>}
      </div>
  );
};