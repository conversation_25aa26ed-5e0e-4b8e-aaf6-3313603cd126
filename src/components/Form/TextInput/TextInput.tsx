/*
 * Copyright 2023 (c) Jaguar Land Rover Ltd. All rights reserved.
 */

import { CSSProperties } from "react";
import { FormComponentProps } from "types/form-component-props";
import styles from "./TextInput.module.scss";
import { InputContainer } from "../";

type Size = "sm" | "md";

const sizeMap: Record<Size, string> = {
  sm: styles.sm,
  md: styles.md,
};

interface TextInputProps extends FormComponentProps {
  value: string;
  placeholder?: string;
  showErrorMsg?: boolean;
  size?: Size;
  style?: CSSProperties;
  onChange: (value: string) => void;
}

export const TextInput = (props: TextInputProps) => {
  const {
    value,
    label,
    description,
    error,
    showErrorMsg = true,
    placeholder,
    size = "md",
    style = {},
    isRequired,
    onChange,
  } = props;

  const classNames = `${styles.input} ${sizeMap[size]} ${error && styles.hasError}`;

  return (
    <InputContainer
      description={description}
      error={showErrorMsg ? error : ""}
      isRequired={isRequired}
      label={label}
      render={(ID) => (
        <input
          className={classNames}
          id={ID}
          onChange={({ target }) => {
            onChange(target.value);
          }}
          placeholder={placeholder}
          style={style}
          type="text"
          value={value}
        />
      )}
    />
  );
};
