/*
 * Copyright 2023 (c) Jaguar Land Rover Ltd. All rights reserved.
 */

import { FormComponentProps } from "types/form-component-props";
import styles from "./Select.module.scss";
import { InputContainer } from "..";

export interface Option {
  label: string;
  value: string;
}

interface SelectProps<T> extends FormComponentProps {
  value?: T;
  options: Option[];
  onChange: (value: T) => void;
}

export const Select = <T extends string>(props: SelectProps<T>) => {
  const { value, options, onChange, label, description, error, isRequired } = props;

  return (
    <InputContainer
      description={description}
      error={error}
      isRequired={isRequired}
      label={label}
      render={(ID) => (
        <select
          className={styles.select}
          id={ID}
          onChange={({ target }) => onChange(target.value as T)}
          value={value}
        >
          <option hidden value="">
            Select
          </option>
          {options.map((option) => {
            return (
              <option key={option.label} value={option.value}>
                {option.label}
              </option>
            );
          })}
        </select>
      )}
    />
  );
};
