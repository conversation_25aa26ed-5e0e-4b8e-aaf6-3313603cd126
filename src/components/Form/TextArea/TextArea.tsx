/*
 * Copyright 2024 (c) Jaguar Land Rover Ltd. All rights reserved.
 */

import { CSSProperties } from "react";
import { FormComponentProps } from "types/form-component-props";
import styles from "./TextArea.module.scss";
import { InputContainer } from "../";

interface TextAreaProps extends FormComponentProps {
  value: string;
  placeholder?: string;
  rows?: number;
  cols?: number;
  showErrorMsg?: boolean;
  style?: CSSProperties;
  onChange: (value: string) => void;
}

export const TextArea = (props: TextAreaProps) => {
  const {
    value,
    label,
    error,
    description,
    isRequired,
    showErrorMsg = true,
    placeholder,
    rows = 5,
    cols = 0,
    style = {},
    onChange,
  } = props;
  const classNames = `${styles.input} ${error && styles.hasError}`;
  return (
    <InputContainer
      description={description}
      error={showErrorMsg ? error : ""}
      isRequired={isRequired}
      label={label}
      render={(ID) => (
        <textarea
          className={classNames}
          cols={cols}
          id={ID}
          name={ID}
          onChange={({ target }) => {
            onChange(target.value);
          }}
          placeholder={placeholder}
          rows={rows}
          style={style}
          value={value}
        />
      )}
    />
  );
};
