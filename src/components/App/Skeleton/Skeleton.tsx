/*
 * Copyright 2022 (c) Jaguar Land Rover Ltd. All rights reserved.
 */

import { Outlet } from "react-router-dom";
import { useContext, useState } from "react";
import { AuthContext } from "context/auth.context";
import { Spinner } from "components/Spinner";
import { Navbar } from "../Navbar";
import { Sidebar } from "../Sidebar";

export const Skeleton = () => {
  const [sidebar, setSidebar] = useState(true);
  const showSidebar = () => {
    setSidebar(!sidebar);
  };
  return (
    <>
      <Navbar showSidebar={showSidebar} sidebar={sidebar} />
      <div id="layout">
        <Sidebar sidebar={sidebar} />
        <AppContent />
      </div>
    </>
  );
};

const AppContent = () => {
  const { loginStatus } = useContext(AuthContext);

  if (loginStatus === "checking") return <Spinner />;
  if (loginStatus === "loggedin") return <Outlet />;

  return (
    <main className="container-fluid" role="alert" style={{ marginTop: "5rem" }}>
      <div style={{ padding: "4rem" }}>
        <h1>Something went wrong with user authentication</h1>
        <p>
          Please try again. If the problem persists, click{" "}
          <a href="https://confluence.devops.jlr-apps.com/display/VCDP/Config+Center+GUI">here</a>{" "}
          for troubleshooting solutions or contact IT.
        </p>
        <button
          className="btn btn-lg btn-outline-dark m-2"
          onClick={() => window.location.reload()}
        >
          Try Again
        </button>
      </div>
    </main>
  );
};
