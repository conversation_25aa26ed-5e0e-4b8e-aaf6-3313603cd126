/*
 * Copyright (c) Jaguar Land Rover Ltd 2022. All rights reserved
 */

import { useEffect, useState } from "react";
import { UpToTopIcon } from "components/Icons";
import styles from "./Navbar.module.scss";

export const ScrollToTop = () => {
  const [showTopBtn, setShowTopBtn] = useState(false);

  useEffect(() => {
    window.addEventListener("scroll", () => {
      if (window.scrollY > 20) {
        setShowTopBtn(true);
      } else {
        setShowTopBtn(false);
      }
    });
  }, []);

  const goToTop = () => {
    window.scrollTo({
      behavior: "smooth",
      top: 0
    });
  };

  return (
    showTopBtn && (
      <button className={styles.iconPosition} onClick={goToTop}>
        <UpToTopIcon />
      </button>
    )
  );
};
