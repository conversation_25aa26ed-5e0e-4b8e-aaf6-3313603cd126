/*
 * Copyright 2024 (c) Jaguar Land Rover Ltd. All rights reserved.
 */

import { NavLink } from "react-router-dom";
import { useContext } from "react";
import { getEnvName } from "utils/constants";
import { CloseIcon, MenuIcon } from "components/Icons";
import { AuthContext } from "context/auth.context";
import styles from "./Navbar.module.scss";
import { ScrollToTop } from "./ScrollToTop";

interface NavbarProps {
  sidebar: boolean;
  showSidebar: () => void;
}

export const Navbar = ({ sidebar, showSidebar }: NavbarProps) => {
  const { firstName, lastName } = useContext(AuthContext);
  return (
    <header className={styles.navbar}>
      <ScrollToTop />
      <div className={styles.navbarExpander}>
        <button
          aria-label="Toggle navigation"
          className="navbar-toggler"
          onClick={showSidebar}
          type="button"
        >
          {sidebar ? <CloseIcon size="36" /> : <MenuIcon color="white" size="36" />}
        </button>

        <h1 className={styles.navTitle}>
          <NavLink to="/">VCDP Config Center</NavLink>
        </h1>
      </div>

      <div className={styles.navInfo}>
        {firstName && (
          <>
            <p>
              {firstName} {lastName}
            </p>
            <div className={styles.verticalLine} />
          </>
        )}
        <p>
          Env: <span>{getEnvName()}</span>
        </p>
      </div>
    </header>
  );
};
