/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

import { BrowserRouter, Route, Routes } from "react-router-dom";
import { ErrorBoundary } from "react-error-boundary";
import { VehicleContextProvider } from "context/vehicle.context";
import { PrivateRoute } from "components/App/PrivateRoute";
import { Skeleton } from "components/App/Skeleton";
import { AuthContextProvider } from "context/auth.context";
import { NotificationContextProvider } from "context/notification.context";
import { Notifications } from "components/Notifications";
import { Home } from "pages/Home";
import { Config } from "pages/Config";
import { Control } from "pages/Control";
import { DataProduct } from "pages/DataProduct";
import { DigitalVehicle } from "pages/DigitalVehicle";
import { ErrorFallback, NotFoundError } from "pages/Errors";
import { RequestHistory } from "pages/RequestHistory";
import { ForgeRockIdentity } from "pages/ForgerockIdentity";
import { FleetManagement } from "pages/FleetManagement";
import { ReactQueryContextProvider } from "context/react-query.context";
import { FleetContextProvider } from "context/fleet.context";
import { Obg } from "pages/Obg";

export const App = () => (
  <BrowserRouter>
    <ErrorBoundary FallbackComponent={ErrorFallback}>
      <AuthContextProvider>
        <NotificationContextProvider>
          <ReactQueryContextProvider>
            <Routes>
              <Route element={<Skeleton />} path="/">
                <Route element={<Home />} index />
                <Route
                  element={
                    <PrivateRoute feature="Config">
                      <Config />
                    </PrivateRoute>
                  }
                  path="/config-request"
                />
                <Route
                  element={
                    <PrivateRoute feature="Control">
                      <Control />
                    </PrivateRoute>
                  }
                  path="/control-request"
                />
                <Route
                  element={
                    <PrivateRoute feature="DataProduct">
                      <VehicleContextProvider>
                        <DataProduct />
                      </VehicleContextProvider>
                    </PrivateRoute>
                  }
                  path="/data-product-request"
                />
                <Route
                  element={
                    <PrivateRoute feature="DigitalVehicle">
                      <DigitalVehicle />
                    </PrivateRoute>
                  }
                  path="/digital-vehicle"
                />
                <Route
                  element={
                    <PrivateRoute feature="RequestHistory">
                      <RequestHistory />
                    </PrivateRoute>
                  }
                  path="/request-history"
                />
                <Route element={<ForgeRockIdentity />} path="/forgerock-identity" />
                <Route
                  element={
                    <PrivateRoute feature="FleetManagement">
                      <FleetContextProvider>
                        <FleetManagement />
                      </FleetContextProvider>
                    </PrivateRoute>
                  }
                  path="/fleet-management"
                />
                <Route
                  element={
                    <PrivateRoute feature="Obg">
                      <Obg />
                    </PrivateRoute>
                  }
                  path="/obg"
                />
                <Route element={<NotFoundError />} path="*" />
              </Route>
            </Routes>
          </ReactQueryContextProvider>
          <Notifications />
        </NotificationContextProvider>
      </AuthContextProvider>
    </ErrorBoundary>
  </BrowserRouter>
);
