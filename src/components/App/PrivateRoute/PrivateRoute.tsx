/*
 * Copyright (c) Jaguar Land Rover Ltd 2022. All rights reserved
 */

import { ReactElement } from "react";
import { ErrorPage } from "components/Errors";
import { usePermissions } from "hooks/use-permissions";
import { Feature } from "types/permissions";

interface PrivateRouteProps {
  feature: Feature;
  children: ReactElement;
}

export function PrivateRoute({ feature, children }: PrivateRouteProps) {
  const { hasPermission } = usePermissions();

  if (hasPermission(feature)) {
    return children;
  }
  return <ErrorPage error="You Do Not Have Access To This Page" />;
}
