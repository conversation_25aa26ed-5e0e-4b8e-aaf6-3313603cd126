/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

import { NavLink } from "react-router-dom";
import { useEffect, useState } from "react";
import { kc } from "services/keycloak.service";
import { LogOutIcon } from "components/Icons";
import { randomID } from "utils/random";
import styles from "./Sidebar.module.scss";
import { sidebarItems } from "./items";

interface SidebarProps {
  sidebar: boolean;
}

export function Sidebar({ sidebar }: SidebarProps) {
  const [width, setWidth] = useState(window.innerWidth);

  useEffect(() => {
    function handleResize() {
      setWidth(window.innerWidth);
    }
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [width]);

  return (
    <nav className={`${styles.sidebarMenu} ${sidebar && styles.active}`}>
      {sidebarItems.map((item) => {
        return (
          <div id={item.id} key={randomID()}>
            <NavLink
              className={({ isActive }) => `${styles.navText} ${isActive && styles.active}`}
              target={item.external ? "_blank" : ""}
              to={item.path}
            >
              {item.icon} {item.title}
            </NavLink>
            {item.divide && <hr />}
          </div>
        );
      })}
      <button className={styles.navText} id="navbar-logOut" onClick={() => kc.logout()}>
        <LogOutIcon /> Log Out
      </button>
    </nav>
  );
}
