/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */
import { ReactElement } from "react";
import { HelpIcon, HomeIcon } from "components/Icons";

interface SideBarItemsProps {
  title: string;
  path: string;
  id: string;
  icon?: ReactElement;
  external?: boolean;
  onClick?: () => void;
  divide?: boolean;
}

export const sidebarItems: SideBarItemsProps[] = [
  {
    title: "Home",
    path: "/",
    id: "navbar-home",
    icon: <HomeIcon />,
    divide: true,
  },
  {
    title: "Config Request",
    path: "/config-request",
    id: "navbar-config",
  },
  {
    title: "Control Request",
    path: "/control-request",
    id: "navbar-control",
  },
  {
    title: "Data Product Request",
    path: "/data-product-request",
    id: "navbar-dataProduct",
  },
  {
    title: "Digital Vehicle",
    path: "/digital-vehicle",
    id: "navbar-digitalVehicle",
  },
  {
    title: "Request History",
    path: "/request-history",
    id: "navbar-requestHistory",
  },
  {
    title: "ForgeRock Identity",
    path: "/forgerock-identity",
    id: "navbar-forgeRock",
  },
  {
    title: "Fleet Management",
    path: "/fleet-management",
    id: "navbar-fleetManagement",
  },
  {
    title: "OBG",
    path: "/obg",
    id: "navbar-obg",
    divide: true,
  },
  {
    title: "Help",
    path: "https://confluence.devops.jlr-apps.com/display/VCDP/Config+Center+GUI",
    id: "navbar-help",
    icon: <HelpIcon />,
    external: true,
  },
];
