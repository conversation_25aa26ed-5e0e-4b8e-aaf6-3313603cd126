/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

import { ReactNode, useState } from "react";
import styles from "./Accordian.module.scss";

interface AccordionItemProps {
  heading: string;
  content: ReactNode;
  permission?: boolean;
}

interface AccordionProps {
  items: AccordionItemProps[];
}

export const Accordian = ({ items }: AccordionProps) => {
  const [activeIndex, setActiveIndex] = useState<number | null>(null);

  const toggleAccordion = (index: number) => {
    setActiveIndex(activeIndex === index ? null : index);
  };

  return (
    <div className={styles.accordion}>
      {items.map((item, index) =>
        item.permission ?? true ? (
          <div className={styles.accordionItem} key={index}>
            <div
              className={`${styles.accordionHeader} ${activeIndex === index ? styles.active : ""}`}
              onClick={() => toggleAccordion(index)}
              onKeyDown={(event) => {
                if (event.key === " ") {
                  toggleAccordion(index);
                }
              }}
            >
              <span>{item.heading}</span>
              <span
                className={`${styles.accordionArrow} ${activeIndex === index ? styles.down : ""}`}
              >
                &#9662;
              </span>
            </div>
            {activeIndex === index && <div className={styles.accordionContent}>{item.content}</div>}
          </div>
        ) : null
      )}
    </div>
  );
};
