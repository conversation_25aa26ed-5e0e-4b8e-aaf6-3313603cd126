/*
 * Copyright 2023 (c) Jaguar Land Rover Ltd. All rights reserved.
 */

import { ReactNode } from "react";
import styles from "./Card.module.scss";

interface ButtonProps {
  text?: string;
  onClick: () => void;
}

interface CardProps {
  children?: ReactNode;
  title?: string | ReactNode;
  cardStyles?: string;
  bodyStyles?: string;
  hasBody?: boolean;
  isGrey?: boolean;
  hasHeader?: boolean;
  animate?: boolean;
  hasButton?: ButtonProps;
}

export function Card(props: CardProps) {
  const {
    children,
    title,
    cardStyles = "mb-3",
    bodyStyles = "",
    hasBody = true,
    isGrey = false,
    hasHeader = true,
    animate = false,
    hasButton,
  } = props;

  const { text = "EDIT", onClick = () => {} } = hasButton || {};

  let classNames = styles.cardHeaderColors;
  if (isGrey) classNames = styles.isGrey;
  const buttonForGreyCard = isGrey
    ? `${styles.hasButton} ${styles.hasButtonGrey}`
    : styles.hasButton;

  return (
    <div className={`card ${cardStyles} ${animate ? styles.animateCard : ""}`}>
      {hasHeader && (
        <div className={`card-header ${classNames}`}>
          <div className={buttonForGreyCard}>
            {title}
            {!!hasButton && <button onClick={onClick}>{text}</button>}
          </div>
        </div>
      )}
      <div className={`${hasBody ? "card-body" : ""} ${bodyStyles}`}>{children}</div>
    </div>
  );
}
