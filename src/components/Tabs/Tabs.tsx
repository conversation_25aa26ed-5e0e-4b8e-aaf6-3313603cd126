/*
 * Copyright 2024 (c) Jaguar Land Rover Ltd. All rights reserved.
 */

import { KeyboardEvent, ReactNode, useContext, useRef } from "react";
import { CSSTransition, SwitchTransition } from "react-transition-group";
import { TabsContext } from "context/tabs.context";
import { Feature } from "types/permissions";
import { usePermissions } from "hooks/use-permissions";
import styles from "./Tabs.module.scss";

export interface Tab {
  key: string;
  tab: string | ReactNode;
  panel: string | ReactNode;
  permission?: Feature | undefined;
}

interface TabsProps {
  data: Tab[];
}

export const Tabs = ({ data }: TabsProps) => {
  const { hasPermission } = usePermissions();
  const tabsRef = useRef<Array<null | HTMLButtonElement>>([]);
  const nodeRef = useRef(null);

  const { activeTab, setActiveTab } = useContext(TabsContext);

  const onKeyDown = ({ code }: KeyboardEvent) => {
    let index = data.findIndex(({ key }) => key === activeTab);

    if (["ArrowRight", "ArrowDown"].includes(code)) {
      index++;
      if (index > data.length - 1) index = 0;
    } else if (["ArrowLeft", "ArrowUp"].includes(code)) {
      index--;
      if (index < 0) index = data.length - 1;
    }

    tabsRef.current[index]?.focus();
    setActiveTab(data[index].key);
  };

  const renderTab = (key: string, tab: ReactNode, isActive: boolean, index: number) => {
    return (
      <button
        aria-selected={isActive}
        key={key}
        onClick={() => setActiveTab(key)}
        onKeyDown={onKeyDown}
        ref={(el) => (tabsRef.current[index] = el)}
        role="tab"
        tabIndex={isActive ? 0 : -1}
      >
        {tab}
      </button>
    );
  };

  const activePanel = data.find(({ key }) => key === activeTab);

  return (
    <div>
      <div className={styles.tabsWrapper}>
        <div className={styles.list} role="tablist">
          {data.map(({ key, tab, permission }, index) => {
            const isActive = key === activeTab;

            return permission === undefined
              ? renderTab(key, tab, isActive, index)
              : hasPermission(permission as Feature) && renderTab(key, tab, isActive, index);
          })}
        </div>
      </div>

      <SwitchTransition>
        <CSSTransition
          classNames="fade"
          exit={false}
          key={activeTab}
          nodeRef={nodeRef}
          timeout={200}
        >
          <div className={styles.panel} ref={nodeRef} role="tabpanel">
            {activePanel?.panel}
          </div>
        </CSSTransition>
      </SwitchTransition>
    </div>
  );
};
