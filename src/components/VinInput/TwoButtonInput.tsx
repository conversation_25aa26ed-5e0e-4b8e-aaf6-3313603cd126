/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

interface TwoButtonInputProps {
  onVinRemoved: () => void;
  onVinAdded: () => void;
  setVinEntered: (val: string) => void;
  vinEntered: string;
}

export const TwoButtonInput = ({
  onVinAdded,
  onVinRemoved,
  vinEntered,
  setVinEntered,
}: TwoButtonInputProps) => {
  return (
    <div className="d-flex flex-row my-2 gap-2">
      <div className="input-group">
        <input
          className="form-control"
          onChange={(ev) => setVinEntered(ev.target.value.replace(/\s/g, ""))}
          placeholder="Enter VIN"
          type="search"
          value={vinEntered}
        />
      </div>
      <button
        className="btn btn-dark noWrap"
        disabled={vinEntered === ""}
        onClick={onVinAdded}
        role="button"
      >
        Add
      </button>
      <button
        className="btn btn-dark noWrap"
        disabled={vinEntered === ""}
        onClick={onVinRemoved}
        role="button"
      >
        Remove
      </button>
    </div>
  );
};
