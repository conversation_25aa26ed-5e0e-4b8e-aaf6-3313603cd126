/*
 * Copyright (c) Jaguar Land Rover Ltd 2022. All rights reserved
 */

import { ChangeEvent, FormEvent } from "react";

import { useNotification } from "hooks/use-notification.hook";
import { formatValidation } from "utils/format-validation";

interface CsvInputProps {
  disableButton: boolean;
  setDisableButton: (val: boolean) => void;
  onVinsUploaded: () => void;
  setVinsEnteredAsCsv: (val: string[]) => void;
  setInvalidVinsFromCsv?: (val: string[]) => void;
}

export function CsvInput(props: CsvInputProps) {
  const {
    disableButton,
    setDisableButton,
    onVinsUploaded,
    setVinsEnteredAsCsv,
    setInvalidVinsFromCsv,
  } = props;

  const { notify } = useNotification();

  const addFormattedVins = (vins: string[]) => {
    if (vins.length === 0) {
      setVinsEnteredAsCsv([]);
      setInvalidVinsFromCsv?.([]);
      setDisableButton(true);
      notify({
        type: "error",
        title: "No VINs found in CSV file",
      });
    } else {
      const { formattedVins, invalidVins } = formatValidation(vins);
      setVinsEnteredAsCsv(formattedVins);
      setInvalidVinsFromCsv?.(invalidVins);
    }
  };

  const handleOnChange = (event: ChangeEvent<HTMLInputElement>) => {
    event.preventDefault();
    setDisableButton(false);
    if (!event.target.files || event.target.files.length === 0) {
      if (setInvalidVinsFromCsv) setInvalidVinsFromCsv([]);
      setVinsEnteredAsCsv([]);
      setDisableButton(true);
      return;
    }
    const file = event.target.files[0];
    if (file) {
      const fileReader = new FileReader();
      fileReader.onload = handleFileRead;
      fileReader.readAsText(file);
    }
  };

  const handleFileRead = (event: ProgressEvent<FileReader>) => {
    if (!event.target?.result) return;
    const csvOutput = event.target.result as string;
    const vins = csvOutput.split("\n").filter((vin) => vin.trim() !== "");
    addFormattedVins(vins);
  };

  const handleUpload = (event: FormEvent) => {
    event.preventDefault();
    setDisableButton(true);
    onVinsUploaded();
  };

  return (
    <form className="d-flex flex-row my-3 gap-2" onSubmit={handleUpload}>
      <div className="browseFileInputGrp">
        <input
          accept=".csv"
          aria-label="csvFileUpload"
          className="form-control"
          onChange={handleOnChange}
          type="file"
        />
      </div>
      <button className="btn btn-dark noWrap" disabled={disableButton} type="submit">
        Upload
      </button>
    </form>
  );
}
