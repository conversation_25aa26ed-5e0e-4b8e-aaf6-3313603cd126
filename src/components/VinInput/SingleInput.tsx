/*
 * Copyright (c) Jaguar Land Rover Ltd 2022. All rights reserved
 */

import { FormEvent } from "react";

interface SingleInputProps {
  onVinEntered: (ev: FormEvent) => void;
  setVinEntered: (val: string) => void;
  buttonText?: string;
}

export function SingleInput(props: SingleInputProps) {
  const { buttonText = "Search", onVinEntered, setVinEntered } = props;

  return (
    <form className="d-flex flex-row my-3 gap-2" onSubmit={onVinEntered}>
      <div className="input-group">
        <input
          className="form-control"
          onChange={(ev) => setVinEntered(ev.target.value.replace(/\s/g, ""))}
          placeholder="Enter VIN"
          type="search"
        />
      </div>
      <button className="btn btn-dark noWrap" type="submit">
        {buttonText}
      </button>
    </form>
  );
}
