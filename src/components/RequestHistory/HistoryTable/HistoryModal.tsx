/*
 * Copyright (c) Jaguar Land Rover Ltd 2022. All rights reserved
 */

import J<PERSON><PERSON>retty from "react-json-pretty";

interface HistoryModalProps {
  configFileName: string;
  modalContent: unknown;
  show: boolean;
  onHide: () => void;
}

export function HistoryModal(props: HistoryModalProps) {
  const { configFileName, modalContent } = props;
  return (
    <div
      aria-hidden="true"
      aria-labelledby="historyModalLabel"
      className="modal"
      id="historyModal"
      tabIndex={-1}
    >
      <div className="modal-dialog modal-dialog-centered modal-lg" role="document">
        <div className="modal-content">
          <div className="modal-header">
            <h5 className="modal-title" id="contained-modal-title-vcenter">
              {configFileName}
            </h5>
            <button
              aria-label="Close"
              className="btn-close"
              data-bs-dismiss="modal"
              role="button"
            />
          </div>

          <div className="modal-body">
            <JSONPretty data={modalContent} id="json-pretty" />
          </div>
        </div>
      </div>
    </div>
  );
}
