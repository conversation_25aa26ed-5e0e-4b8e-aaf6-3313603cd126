/*
 * Copyright (c) Jaguar Land Rover Ltd 2022. All rights reserved
 */

import { useState } from "react";
import { ENV_CONTEXT } from "utils/constants";
import { get } from "services/fetch.service";
import { Pagination } from "components/Pagination";
import { usePagination } from "hooks/use-pagination.hook";
import { ConfigLogFile, ConfigLogMeta } from "types/data";
import { Spinner } from "components/Spinner";
import { useNotification } from "hooks/use-notification.hook";
import { useModalHandler } from "hooks/use-modal-handler.hook";
import { randomID } from "utils/random";
import { HistoryModal } from "./HistoryModal";

interface HistoryTableProps {
  historyData: ConfigLogMeta[];
  loading: boolean;
}

export function HistoryTable({ loading, historyData }: HistoryTableProps) {
  const [modalShow, setModalShow] = useState(false);
  const [modalContent, setModalContent] = useState<unknown>();
  const [configFileName, setConfigFileName] = useState("");
  const { notify } = useNotification();

  const { useModal } = useModalHandler("historyModal");

  const formatDate = (date: Date) => {
    const formattedDate = new Date(date);
    return formattedDate.toUTCString();
  };

  const { data, handlePageChange, page, rowsPerPage, setPage, setRowsPerPage, total } =
    usePagination(historyData);

  const viewConfigFile = async (history: ConfigLogMeta) => {
    try {
      const { data, status } = await get<ConfigLogFile>(
        `${ENV_CONTEXT.crl_uri}/config-log/getbyid?id=${history.id}`
      );
      if (status === 200) {
        setConfigFileName(history.configurationType);
        setModalContent(data.config);
        useModal?.show();
      } else {
        notify({
          type: "error",
          title: "Error Retrieving Configuration",
        });
      }
    } catch (error) {
      if (error instanceof Error) {
        notify({
          type: "error",
          title: error.message,
        });
      }
    }
  };

  const displayTableBody = () => {
    if (loading) {
      return (
        <tr className="text-center" style={{ lineHeight: "2.5" }}>
          <td colSpan={5}>
            <Spinner />
          </td>
        </tr>
      );
    }

    if (!data || data.length === 0) {
      return (
        <tr className="text-center" style={{ lineHeight: "2.5" }}>
          <td colSpan={5}>Create search request to view table content</td>
        </tr>
      );
    }
    return data.map((history: ConfigLogMeta) => (
      <tr key={randomID()} style={{ lineHeight: "2.5" }}>
        <td>{history.requester}</td>
        <td>{formatDate(history.created)}</td>
        <td>{history.configurationType}</td>
        <td>
          <button className="btn btn-dark" onClick={() => viewConfigFile(history)}>
            View
          </button>
        </td>
        <td>
          <button
            className="btn btn-dark"
            onClick={() => {
              setModalContent(history.vins);
              useModal?.show();
            }}
          >
            View
          </button>
        </td>
      </tr>
    ));
  };

  return (
    <div className="container-fluid">
      <HistoryModal
        configFileName={configFileName}
        modalContent={modalContent}
        onHide={() => setModalShow(false)}
        show={modalShow}
      />
      <table aria-label="historyTable" className="table table-bordered table-heading">
        <thead>
          <tr>
            <th>Requested</th>
            <th>Created</th>
            <th>Configuration Type</th>
            <th>Configuration File</th>
            <th>Vehicle(s)</th>
          </tr>
        </thead>
        <tbody>{displayTableBody()}</tbody>
      </table>
      <Pagination
        handlePageChange={handlePageChange}
        page={page}
        rowsPerPage={rowsPerPage}
        setPage={setPage}
        setRowsPerPage={setRowsPerPage}
        total={total}
      />
    </div>
  );
}
