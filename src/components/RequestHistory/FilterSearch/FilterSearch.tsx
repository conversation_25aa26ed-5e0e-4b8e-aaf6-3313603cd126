/*
 * Copyright (c) Jaguar Land Rover Ltd 2022. All rights reserved
 */

import { MouseEvent, useState } from "react";
import { ENV_CONTEXT } from "utils/constants";
import { post } from "services/fetch.service";
import { ConfigLogMeta, defaultFilter, SearchFilter } from "types/data";
import { Card } from "components/Card";
import { useNotification } from "hooks/use-notification.hook";

interface FilterSearchProps {
  setHistoryData: (val: ConfigLogMeta[]) => void;
  setLoading: (val: boolean) => void;
}

const selectOptions = [
  { name: "Any", value: "ANY" },
  { name: "Query", value: "QUERY" },
  { name: "Ruleset", value: "RULESET" },
  { name: "Signal Dictionary", value: "SIGNAL_DICTIONARY" },
  { name: "Diagnostic Dictionary", value: "DIAGNOSTIC_DICTIONARY" },
  { name: "Channel", value: "CHANNEL" },
];

export function FilterSearch({ setHistoryData, setLoading }: FilterSearchProps) {
  const [filter, setFilter] = useState<SearchFilter>(defaultFilter);
  const { notify } = useNotification();

  const cleanRequest = () => {
    const searchBody = filter;
    if (filter.startDate === defaultFilter.startDate) delete searchBody.startDate;
    if (filter.endDate !== defaultFilter.endDate) delete searchBody.endDate;
    if (filter.vin?.length === 0) delete searchBody.vin;
    return searchBody;
  };

  const sendFilterRequest = async (event: MouseEvent) => {
    event.preventDefault();
    if (filter.requester === "") {
      notify({
        type: "warning",
        title: "Please Enter a Requester Name",
      });
    } else {
      try {
        setLoading(true);
        const request = cleanRequest();

        const { data, status } = await post<ConfigLogMeta[]>(
          `${ENV_CONTEXT.crl_uri}/config-log/search`,
          request
        );

        if (status === 204) {
          notify({
            type: "warning",
            title: "No Content Available For Filters Selected",
          });

          setLoading(false);
          setHistoryData([]);
        } else if (status === 200) {
          setHistoryData(data);
          setLoading(false);
        } else {
          notify({
            type: "error",
            title: "Error Retrieving History",
          });

          setLoading(false);
        }
      } catch (error) {
        if (error instanceof Error) {
          notify({
            type: "error",
            title: error.message,
          });
        }

        setLoading(false);
      }
    }
  };

  return (
    <Card title="Request History">
      <form autoComplete="off">
        <div className="form-group">
          <label className="boldLabel" htmlFor="configTypeForm">
            Configuration Type
          </label>
          <select
            aria-label="Default select example"
            className="form-select"
            defaultValue={filter.configurationType}
            id="configTypeForm"
            onChange={(event) =>
              setFilter((prevValues) => ({ ...prevValues, configurationType: event.target.value }))
            }
          >
            {selectOptions.map((selectOption) => {
              return (
                <option key={selectOption.value} value={selectOption.value}>
                  {selectOption.name}
                </option>
              );
            })}
          </select>
        </div>

        <div className="form-group mt-3">
          <label className="boldLabel" htmlFor="vinForm">
            Vehicle Identifier
          </label>
          <input
            className="form-control"
            id="vinForm"
            onChange={(event) =>
              setFilter((prevValues) => ({ ...prevValues, vin: event.target.value }))
            }
            placeholder="Enter VIN"
            type="text"
          />
        </div>

        <div className="form-group mt-3">
          <label className="boldLabel" htmlFor="nameForm">
            Requester Name <span className="requiredRed">*</span>
          </label>
          <input
            className="form-control"
            id="nameForm"
            onChange={(event) =>
              setFilter((prevValues) => ({ ...prevValues, requester: event.target.value }))
            }
            placeholder="Enter Requester Name"
            type="text"
          />
        </div>

        <div className="form-group mt-3">
          <label className="boldLabel" htmlFor="startDateForm">
            Start Date
          </label>
          <input
            className="form-control"
            id="startDateForm"
            max={new Date().toISOString().split("T")[0]}
            onChange={(event) =>
              setFilter((prevValues) => ({
                ...prevValues,
                startDate: event.target.value,
              }))
            }
            placeholder="date from"
            type="date"
          />
        </div>

        <div className="form-group mt-3">
          <label className="boldLabel" htmlFor="endDateForm">
            End Date
          </label>
          <input
            className="form-control"
            id="endDateForm"
            max={new Date().toISOString().split("T")[0]}
            min={filter.startDate}
            onChange={(event) =>
              setFilter((prevValues) => ({
                ...prevValues,
                endDate: event.target.value,
              }))
            }
            placeholder="date to"
            type="date"
          />
        </div>

        <span className="requiredRed">*Required</span>

        <div className="text-center">
          <button className="btn btn-dark" onClick={(event) => sendFilterRequest(event)}>
            Search Requests
          </button>
        </div>
      </form>
    </Card>
  );
}
