/*
 * Copyright 2023 (c) Jaguar Land Rover Ltd. All rights reserved.
 */

import { ENV_CONTEXT } from "utils/constants";
import {
  DataProductPolicy,
  DataResponse,
  FeatureCodes,
  TestRigRequest,
  UpdateObgApplicationRequest,
  Vehicle,
  VehicleTags,
} from "types/data";
import { del, patch, post, put } from "services/fetch.service";
import { PatchVehicle } from "types/patch-vehicle";
import featureCodes from "../utils/feature-codes.json";

const oldDvEndPoint = `${ENV_CONTEXT.dv_uri}/vehicles`;
const dvBaseEndPoint = `${ENV_CONTEXT.dv_uri}/v2/vehicles`;
const lambdaBaseEndpoint = `${ENV_CONTEXT.aws_gateway_uri}`;

type PatchRequest = { patchVehicleRequest: PatchVehicle; vin: string };

export const getVehicles = ({ vins, inclusions }: { vins: string[]; inclusions?: string[] }) => {
  return post<DataResponse<Vehicle>>(`${dvBaseEndPoint}/?identifierType=VIN`, {
    identifiers: vins,
    inclusions: inclusions,
  });
};

export const updateDataProductPolicies = (vins: string[]) => {
  return post<DataResponse<DataProductPolicy>>(
    `${dvBaseEndPoint}/policies/dataProduct/?identifierType=VIN`,
    {
      identifiers: vins,
    }
  );
};

export const updateObgApplication = ({
  uniqueId,
  isEnabled,
}: {
  uniqueId: string;
  isEnabled: boolean;
}) => {
  const obgBody: UpdateObgApplicationRequest = {
    obgApplication: {
      isEnabled: isEnabled,
    },
  };

  return put(`${dvBaseEndPoint}/${uniqueId}/applications/obg`, obgBody);
};

export const addVehicleTags = (tagsRequestData: VehicleTags) => {
  return post(`${dvBaseEndPoint}/tags`, tagsRequestData);
};

export const deleteVehicleTags = (tagsRequestData: VehicleTags) => {
  return del(`${dvBaseEndPoint}/tags`, tagsRequestData);
};

export const postTestRig = (testRigRequest: TestRigRequest) => {
  return post(`${lambdaBaseEndpoint}/createtestrig`, testRigRequest);
};

export const patchVehicle = ({ patchVehicleRequest, vin }: PatchRequest) => {
  return patch(`${oldDvEndPoint}/updateVehicle/${vin}`, patchVehicleRequest);
};

export const getFeatureCodes = (): Promise<FeatureCodes> => {
  return Promise.resolve(featureCodes);
};
