import { ENV_CONTEXT } from "utils/constants";
import { del, get, post, put } from "services/fetch.service";
import { ApplyDataProduct, DPResults, vinIsExcluded } from "types/data";

const baseEndpoint = `${ENV_CONTEXT.dpd2_uri}/api`;

export const getExcludedVins = (vins: string[]) => {
  return get<{ data: vinIsExcluded[] }>(`${baseEndpoint}/excluded-vehicles`, {
    headers: { VIN: vins.join(",") },
  });
};

export const applyDataProduct = (requestArray: ApplyDataProduct[]) => {
  return put<{ results: DPResults[] }>(`${baseEndpoint}/vehicles/data-products`, {
    dataProductsForVehicles: requestArray,
  });
};

// Not excluded === Auto
export const makeVehicleAuto = (vin: string) => {
  return del(`${baseEndpoint}/excluded-vehicles`, { vins: [vin] });
};

// Is excluded === Non-Auto
export const makeVehicleNonAuto = (vin: string) => {
  return post(`${baseEndpoint}/excluded-vehicles`, { vins: [vin] });
};
