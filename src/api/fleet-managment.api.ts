/*
 * Copyright 2024 (c) Jaguar Land Rover Ltd. All rights reserved.
 */

import { del, post, put } from "services/fetch.service";
import { FleetCount, SearchByFleetResponse, VehicleIdentity } from "types/fleet-management";
import { ENV_CONTEXT } from "utils/constants";

interface AddFleetIdProps {
  vins: string[];
  fleetId: string;
}

const baseEndPoint = `${ENV_CONTEXT.dv_uri}/v2/vehicles`;
const SEARCH_LIMIT = 1000;

export const getFleetCount = (fleetId: string) => {
  return post<FleetCount>(`${baseEndPoint}/count`, {
    assets: {
      fleetId: [fleetId],
    },
  });
};

export const searchVehiclesByFleetId = async (fleetId: string) => {
  const vehicleData: VehicleIdentity[] = [];

  const initialResponse = post<SearchByFleetResponse>(`${baseEndPoint}/search`, {
    searchCriteria: {
      assets: {
        fleetId: [fleetId],
      },
    },
    inclusions: ["IDENTITY"],
    ttlMs: 500,
    limit: SEARCH_LIMIT,
  });

  let responseData = (await initialResponse).data;
  vehicleData.push(...responseData.data);
  const scrollId = responseData.meta.scrollId;

  while (responseData.data.length === SEARCH_LIMIT) {
    const scrollResponse = await searchVehiclesByScrollId(scrollId);
    const scrollData = scrollResponse.data;
    vehicleData.push(...scrollData.data);
    responseData = scrollData;
  }

  return vehicleData;
};

export const searchVehiclesByScrollId = (scrollId: string) => {
  return post<SearchByFleetResponse>(`${baseEndPoint}/search/scroll`, {
    scrollId: scrollId,
  });
};

export const addFleetID = ({ vins, fleetId }: AddFleetIdProps) => {
  return put(`${baseEndPoint}/assets/fleetId`, {
    vins: vins,
    fleetId: fleetId,
  });
};

export const deleteFleetID = (vins: string[]) => {
  return del(`${baseEndPoint}/assets/fleetId`, {
    vins: vins,
  });
};
