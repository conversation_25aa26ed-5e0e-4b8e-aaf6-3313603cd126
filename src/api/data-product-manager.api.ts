/*
 * Copyright 2024 (c) Jaguar Land Rover Ltd. All rights reserved.
 */

import { ENV_CONTEXT } from "utils/constants";
import { Delta, DPStatus } from "types/data";
import { get, put } from "services/fetch.service";

interface StatusProps {
  dpId: string;
  status: DPStatus;
}

const baseEndPoint = `${ENV_CONTEXT.dpm_uri}/data-products`;

export const updateDpStatus = ({ dpId, status }: StatusProps) => {
  return put<string>(`${baseEndPoint}/${dpId}/status`, { status });
};

export const getDeltas = () => {
  return get<Delta[]>(`${ENV_CONTEXT.dpm_uri}/data-product/delta`);
}

export const getVehicleTags = () => {
  return get<string[]>(`${ENV_CONTEXT.dpm_uri}/data-product/tags`);
};
