/*
 * Copyright 2024 (c) Jaguar Land Rover Ltd. All rights reserved.
 */

import { post } from "services/fetch.service";
import { ENV_CONTEXT } from "utils/constants";

const baseEndPoint = `${ENV_CONTEXT.obg_uri}`;

export interface ProtocolFormI {
  vehicleId: string;
  protocolPayload: string;
}

export const createProtocol = ({ vehicleId, protocolPayload }: ProtocolFormI) => {
  return post(`${baseEndPoint}/protocol`, {
    vehicleId: vehicleId,
    protocolPayload: protocolPayload,
  });
};
