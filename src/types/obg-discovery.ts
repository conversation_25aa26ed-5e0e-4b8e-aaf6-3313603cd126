/*
 * Copyright 2024 (c) Jaguar Land Rover Ltd. All rights reserved.
 */

export interface ChannelDefinition {
  application: string;
  blacklist: boolean;
  channels: Channel[];
  createdBy: string;
  createdDate: string;
  updatedBy?: string;
  updatedDate?: string;
  permission?: string[];
}

export interface Channel {
  id: string;
  protocol: string;
  endpoint: string;
  priority: string;
  method: string;
}
