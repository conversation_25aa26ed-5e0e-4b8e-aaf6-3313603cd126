/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

export type ResponseError = {
  message: string;
  code: string;
};

export type DataResponse<T> = {
  data: T[];
  meta?: object;
  errors?: ResponseError[];
};

export const DEFAULT_VEHICLE_DATA = {
  data: {
    data: [],
  },
};

export type ConfigLogFile = {
  id: string;
  config: unknown;
};

export type ConfigLogMeta = {
  id: string;
  requester: string;
  created: Date;
  vins: string[];
  configurationType: ConfigLogMetaType;
};

type ConfigLogMetaType = "RULESET" | "SIGNAL_DICTIONARY" | "DIAGNOSTIC_DICTIONARY" | "VA_CONTROLS";

/*
 * request history form
 */
export type SearchFilter = {
  requester?: string;
  configurationType: string;
  startDate?: string;
  endDate?: string;
  vin?: string;
};

export const defaultFilter: SearchFilter = {
  requester: "",
  configurationType: "ANY",
  startDate: "1970-01-01",
  endDate: new Date().toISOString().split("T")[0],
  vin: "",
};

/*
 * /dv-state-handler/v2/vehicles/policies/dataProduct
 */

export type DataProductPolicy = {
  identifier: string;
  active?: DataProductPolicyConfig;
  preceding?: DataProductPolicyConfig;
  pending?: DataProductPolicyConfig;
};

export type DataProductPolicyConfig = {
  timestamp: string;
  hash: string;
  configs: DataProductVersion[];
};

type DataProductVersion = {
  dataProductId: string;
  modified: string;
};

export const defaultPolicy: DataProductPolicy = {
  identifier: "",
  active: {
    timestamp: "",
    hash: "",
    configs: [],
  },
};

/*
 * /data-product-manager/data-product/delta
 */

export type Delta = {
  dataProductId: string;
  status: DPStatus;
  permissionList: string[];
  modified: string;
  created: string;
  schemaVersion: string;
  comment: string;
  eligibilityCriteria: {
    assets: {
      brand: string[];
      modelRange: string[];
      modelYear: string[];
      fuelType: string[];
      fleetId: string[];
    };
    tags: {
      tags: string[];
    };
  };
  queries: ConfigQuery[];
};

export type DPStatus = "ROLLOUT" | "TESTING";

export const defaultDelta: Delta = {
  dataProductId: "",
  status: "ROLLOUT",
  permissionList: [],
  modified: "",
  created: "",
  schemaVersion: "",
  comment: "",
  eligibilityCriteria: {
    assets: {
      brand: [],
      modelRange: [],
      modelYear: [],
      fuelType: [],
      fleetId: [],
    },
    tags: {
      tags: [],
    },
  },
  queries: [
    {
      comment: "",
      id: "",
      while: [{ key: [{}] }],
      when: { key: [{}] },
      emit: [{}],
      out: { chId: [""], eng: 1 },
    },
  ],
};

type ConfigQuery = {
  comment: string;
  id: string;
  while: MapObject[];
  when: MapObject;
  emit: object[];
  out: ChannelDefinition;
};

type MapObject = {
  [key: string]: object[];
};

type ChannelDefinition = {
  chId: string[];
  eng: number;
};

export type VaControls = {
  vin: string;
  uniqueId: string;
  enableVA: string;
  allowWiFi: string;
  enableMirroring: string;
  enablePushData: string;
  lastVaControlsUpdate: string;
};

/*
 * /dv-state-handler/v2/vehicles/tags
 */
export type VehicleTags = {
  vins: string[];
  tag: string;
};

/*
 * /dv-state-handler/v2/vehicles/?identifierType=VIN
 * type = domain/dto/VehicleV2.java
 */
export type Vehicle = {
  id: string;
  identity: Identity;
  assets: Assets;
  policies: Policies;
  inventory: Inventory[];
  applications: Applications;
  tags: string[];
};

export type Identity = {
  uniqueId: string;
  identityCreated: string;
  vin: string;
  squishVin: string;
  vehicleReg: string;
};

export interface ManifestDataTableProps {
  signalDictionary: string;
  signalDictionarySchema: string;
  diagnosticsDictionary: string;
  diagnosticsDictionarySchema: string;
  queries: string;
  queriesSchema: string;
  protocolChannels: string;
  protocolChannelsSchema: string;
}

export type Assets = {
  fleetId: string;
  brand: string;
  modelRange: string;
  targetMarket: string;
  soldIntoMarket: string;
  modelYear: string;
  modelName: string;
  trim: string;
  bodyStyle: string;
  driver: string;
  transmission: string;
  engine: string;
  plant: string;
  fuelType: string[];
  featureCodes: string[];
  vehicleArchitecture: string;
};
type Policies = {
  dataProduct: DataProductPolicy;
  dictionary: DictionaryPolicy;
  vaControls: VaControlsPolicy;
};
type DictionaryPolicy = {
  identifier: string;
  active?: DictionaryPolicyConfig;
  preceding?: DictionaryPolicyConfig;
  pending?: DictionaryPolicyConfig;
};
type DictionaryPolicyConfig = {
  timestamp: string;
  version: string;
};
type VaControlsPolicy = {
  identifier: string;
  active?: VaControlsPolicyConfig;
  preceding?: VaControlsPolicyConfig;
  pending?: VaControlsPolicyConfig;
};
type VaControlsPolicyConfig = {
  timestamp: string;
  controls: Omit<VaControls, "vin" | "uniqueId" | "lastVaControlsUpdate">;
};
export type Inventory = {
  ecuAcronym: string;
  diagnosticVariant: string;
  effectiveFrom: string;
  lastRead: string;
  modifiedBy: string;
  dvSupportedOnboard: boolean;
  softwareInventory: SoftwareInventory[];
  hardwareInventory: HardwareInventory;
  asymKeys: AsymKey[];
  symKeys: SymKey[];
  vkmsTimestamp: string;
};
type SoftwareInventory = {
  swCompId: string;
  swCompVersion: string;
  swCompType: string;
};
type HardwareInventory = {
  ecuNodeAddr: string;
  ecuHwPartNo: string;
  ecuSerialNumber: string;
};

export interface AsymKey {
  keyLabel: string;
  certName: string;
  keyType: string;
  keySize: number;
  certAuthKeyId: string;
  cert: string;
}

export interface SymKey {
  keyLabel: string;
  keyType: string;
  keySize: number;
  keyGenMethod: string;
  keyWrapInfo: KeyWrapInfo;
  kcv: string;
  generationNo: number;
  keyOld: string;
}

export interface KeyWrapInfo {
  keyType: string;
  keySize: number;
  algId: string;
  iv: string;
  keyLabel: string;
}

export type Applications = {
  vehicleAnalytics: VehicleAnalytics;
  digitalVehicle: DigitalVehicle;
  obg: Obg;
  vkms: Vkms;
};

export interface Vkms {
  productId: string;
  dlcInfo: DlcInfo;
  caCerts: CaCert[];
}

export interface DlcInfo {
  id: string;
  type: string;
  ecuList: string[];
  schemaName: string;
  schemaVersion: string;
  generationTime: string;
  dskKcv: string;
}

export interface CaCert {
  certName: string;
  certSubKeyId: string;
  certAuthKeyId: string;
  cert: string;
}

export type VehicleAnalytics = {
  status: Status;
  manifest: Manifest;
  errorMessages: ErrorMessage[];
};
export type Status = Omit<VaControls, "vin" | "uniqueId">;

export type ErrorMessage = {
  errorMetadata: string;
  faultIdentifier: string;
  severity: string;
  timestamp: string;
};
export type Manifest = {
  protocolChannels: string;
  protocolChannelsSchema: string;
  queries: string;
  queriesSchema: string;
  signalDictionary: string;
  signalDictionarySchema: string;
  vaAppSoftwareVersion: string;
  diagnosticsDictionary: string;
  diagnosticsDictionarySchema: string;
  vaConfigAcceptErrorState: string;
  lastManifestUpdate: string;
};
type DigitalVehicle = {
  sotaEnabled: string;
  vehicleInSync: string;
  vehicleModifiedDate: string;
  inventoryModifiedDate: string;
};
export type Obg = {
  isEnabled: boolean;
};

export interface UpdateObgApplicationRequest {
  obgApplication: Obg;
}

/*
 * /config-center-service/controls/va
 */

export type VaToggles = {
  timestamp: string;
  status: string;
  message: string;
};

/*
 * apply data product
 * data-product-delivery/data-product
 */

export type DPResults = {
  vin: string;
  status: string;
};

export interface ApplyDataProduct {
  vin: string;
  dataProductIds: string[];
}

/*
 * /config-center-service/api/v1/config/getmeta
 * /config-center-service/api/v1/config/getbyid?id=:id
 */

export type VehicleContent = {
  data: VehicleConfig[];
  pageNumber: number;
  pageSize: number;
  totalPages: number;
  totalElements: number;
};

export type VehicleConfig = {
  id: string;
  name: string;
  configurationType: ConfigurationType;
  file: KeyValueType;
};

export type ConfigurationType =
  | ""
  | "RULESET"
  | "QUERY"
  | "SIGNAL_DICTIONARY"
  | "DIAGNOSTIC_DICTIONARY"
  | "CHANNEL";

type KeyValueType = {
  [key: string]: unknown;
};

export const defaultVehicleConfig: VehicleConfig[] = [
  {
    id: "",
    name: "",
    configurationType: "" as ConfigurationType,
    file: { a: "" },
  },
];

export type CreateVehicleConfig = {
  message?: string;
  messages?: string[];
};

export type DisableOptionsProps = {
  disableSignal: boolean;
  disableDiagnostic: boolean;
  disableChannel: boolean;
};

/*
 * /data-product-delivery-v2/api/excluded-vehicles
 */

export type vinIsExcluded = {
  vin: string;
  isExcluded: boolean;
};

export type AutoStatusType = "Auto" | "Non-Auto" | "Not Found";

export interface ExcludedVinsI {
  [key: string]: AutoStatusType;
}

// Create Test Rig - /v1/createtestrig
export type TestRigCertType = "PROD" | "ENG" | "";

export type TestRigRequest = {
  vin: string;
  tcu: string;
  cert: TestRigCertType;
};

export type FeatureCodes = {
  featureCodes: string[];
};
