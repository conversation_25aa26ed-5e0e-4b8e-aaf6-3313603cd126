export interface PatchVehicle {
  vehicleReg?: string;
  fleetId?: string;
  brand?: string;
  modelRange?: string;
  targetMarket?: string;
  soldIntoMarket?: string;
  modelYear?: string;
  modelName?: string;
  trim?: string;
  bodyStyle?: string;
  driver?: string;
  transmission?: string;
  engine?: string;
  fuelType?: string[];
  plant?: string;
  featureCodes?: string[];
  inventory?: Inventory[];
  vkms?: Vkms;
}

export interface Inventory {
  ecuAcronym: string;
  diagnosticVariant: string;
  effectiveFrom: string;
  lastRead: string;
  softwareInventory: SoftwareInventory[];
  hardwareInventory: HardwareInventory;
  asymKeys: AsymKey[];
  symKeys: SymKey[];
  vkmsTimestamp?: string;
}

export interface SoftwareInventory {
  swCompId: string;
  swCompVersion: string;
  // Not needed but missing swPartNo - Are they the same?
  // swCompType: string;
}

export interface HardwareInventory {
  ecuNodeAddr: string;
  ecuHwPartNo: string;
  ecuSerialNumber: string;
}

export interface AsymKey {
  keyLabel: string;
  certName: string;
  keyType: string;
  keySize: number;
  certAuthKeyId: string;
  cert: string;
}

export interface SymKey {
  keyLabel: string;
  keyType: string;
  keySize?: number;
  keyGenMethod?: string;
  keyWrapInfo?: KeyWrapInfo;
  kcv: string;
  generationNo: number;
  keyOld?: string;
}

export interface KeyWrapInfo {
  keyType: string;
  keySize: number;
  algId: string;
  iv: string;
  keyLabel: string;
}

export interface Vkms {
  productId: string;
  dlcInfo: DlcInfo;
  caCerts: CaCert[];
}

export interface DlcInfo {
  id: string;
  type: string;
  ecuList: string[];
  schemaName: string;
  schemaVersion: string;
  generationTime: string;
  dskKcv: string;
}

export interface CaCert {
  certName: string;
  certSubKeyId: string;
  certAuthKeyId: string;
  cert: string;
}
