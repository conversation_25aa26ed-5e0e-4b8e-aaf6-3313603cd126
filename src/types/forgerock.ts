/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

export type VehicleResponse = {
  users: string[];
  "fr-client-id": string;
  "fr-vehicle-id": string;
  "public-key-id": string;
  "vin-matching": boolean;
  "vehicle-id-matching": boolean;
  "client-id-matching": boolean;
  "passphrase-matching": boolean;
  dvVehicleExists: boolean;
  frVehicleExists: boolean;
  error: boolean;
  "error-message": string;
};

export interface IdentityI {
  inputValue: string;
  identifierValue: string;
}

export interface TableItemI {
  title: string;
  vehicleItem?: boolean | string;
  usesIcons?: boolean;
}
