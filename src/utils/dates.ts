/*
 * Copyright (c) Jaguar Land Rover Ltd 2022. All rights reserved
 */

import { Delta } from "types/data";
import { ChannelDefinition } from "types/obg-discovery";

export const options: Intl.DateTimeFormatOptions = {
  year: "numeric",
  month: "long",
  day: "numeric",
  hour: "numeric",
  minute: "numeric",
  second: "numeric",
};

export const getDateTime = (timestamp: string | number) => {
  return new Date(timestamp).toLocaleDateString(undefined, options);
};

export const sortByDate = (a: Delta, b: Delta) => {
  return new Date(b.modified).valueOf() - new Date(a.modified).valueOf();
};

export const sortByCreatedDate = (a: ChannelDefinition, b: ChannelDefinition) => {
  return new Date(b.createdDate).valueOf() - new Date(a.createdDate).valueOf();
};
