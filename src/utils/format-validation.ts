/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

export const formatValidation = (vins: string[]) => {
  const invalidVins: string[] = [];
  const formattedVins = vins
    .map((vin) => vin.replace(/[\r\n]+/gm, ""))
    .filter((vin, index, self) => {
      const isValid = /^[A-Z0-9]{17}$/.test(vin) && self.indexOf(vin) === index;
      if (!isValid) {
        invalidVins.push(vin);
      }
      return isValid;
    });

  return { formattedVins, invalidVins };
};
