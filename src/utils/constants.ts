/*
 * Copyright (c) Jaguar Land Rover Ltd 2022. All rights reserved
 */

import { Enviroment } from "types/permissions";

export const getKeycloakConfig = () => {
  const hostname = window.location.hostname;
  if (hostname === "localhost") {
    return {
      clientId: getClientID("local"),
      realm: "Config-Center-GUI",
    };
  }

  const env = extractEnvFromHostname(hostname);
  switch (env) {
    case "pre-prod":
      return {
        clientId: getClientID("pre-prod"),
        realm: "Config-Center-GUI",
      };
    case "preprod":
      return {
        clientId: getClientID("preprod"),
        realm: "Config-Center-GUI",
      };
    case "prod":
      return {
        clientId: getClientID("prod"),
        realm: "Config-Center",
      };

    case "dev":
    default:
      return {
        clientId: getClientID("dev"),
        realm: "Config-Center-GUI",
      };
  }
};

export const getEnvName = () => {
  const hostname = window.location.hostname;
  const env = extractEnvFromHostname(hostname);

  if (env === "pre-prod" || env === "preprod") return "Pre-Production";
  else if (env === "prod") return "Production";
  else return "Development";
};

export const extractEnvFromHostname = (hostname: string): Enviroment => {
  const regex = /vlc-config-center-gui\.(dev|pre-prod|prod)\.jlr-(vcdp)\.com/;
  const regex_cn = /vlc-config-center-gui.cn\-(dev|preprod|prod)\.jlr-(vcdp).dcclouds\.com/; // eslint-disable-line
  const match = regex.exec(hostname);
  const match_cn = regex_cn.exec(hostname);
  // group 1 = full match, group 2 = capture group 1, group 3 is capture group 2
  if (match !== null && match.length === 3) {
    return match[1] as Enviroment;
  } else if (match_cn !== null && match_cn.length === 3) {
    return match_cn[1] as Enviroment;
  }
  return "dev";
};

const getClientID = (env: string): string => {
  const hostname = window.location.hostname;
  let clientId = "config-gui-auth-" + env;
  if (env == "prod") {
    clientId = "config-center-auth-" + env;
  }
  if (hostname.includes("vcdp.dcclouds.com")) {
    clientId = "config-gui-auth-cn-" + env;
  }
  return clientId;
};

const getGatewayUri = (env: string): string => {
  const hostname = window.location.hostname;
  let gatewayUri = `https://vlc-api-gateway.${env}.jlr-vcdp.com`;
  if (hostname.includes("vcdp.dcclouds.com")) {
    gatewayUri = `https://vlc-api-gateway.cn-${env}.jlr-vcdp.dcclouds.com`;
  }
  return gatewayUri;
};

const getKeycloakUri = (env: string): string => {
  const hostname = window.location.hostname;
  let keycloakUri = `https://keycloak-sre.${env}.jlr-vcdp.com`;
  if (hostname.includes("vcdp.dcclouds.com")) {
    keycloakUri = `https://keycloak-apps.cn-${env}.jlr-vcdp.dcclouds.com`;
  }
  return keycloakUri;
};

const buildContext = (env: string) => {
  const VLC_API_GATEWAY_URI = getGatewayUri(env);
  return {
    cc_uri: `${VLC_API_GATEWAY_URI}/config-center-service`,
    crl_uri: `${VLC_API_GATEWAY_URI}/config-request-logger`,
    dpd_uri: `${VLC_API_GATEWAY_URI}/data-product-delivery`,
    dpd2_uri: `${VLC_API_GATEWAY_URI}/data-product-delivery-v2`,
    dpm_uri: `${VLC_API_GATEWAY_URI}/data-product-manager`,
    dv_uri: `${VLC_API_GATEWAY_URI}/dv-state-handler`,
    fd_uri: `${VLC_API_GATEWAY_URI}/forgerock-dashboard`,
    obg_uri: `${VLC_API_GATEWAY_URI}/obg-discovery`,
    aws_gateway_uri: `${VLC_API_GATEWAY_URI}/aws-api-gateway`,
    keycloak_uri: getKeycloakUri(env),
  };
};

const buildEnvContext = () => {
  const hostname = window.location.hostname;
  const env = extractEnvFromHostname(hostname);
  return buildContext(env);
};

export const ENV_CONTEXT = buildEnvContext();
