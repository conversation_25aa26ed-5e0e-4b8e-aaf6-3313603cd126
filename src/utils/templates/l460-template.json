{"vehicleReg": "AA10 1AA", "brand": "LR", "modelRange": "L460", "targetMarket": "GBR", "soldIntoMarket": "GBR", "modelYear": "MY26", "modelName": "Range Rover Evoque II", "trim": "L460 EV EBA31 Standard AWD 5DR SWB", "bodyStyle": "5 DOOR STATION WAGON", "driver": "RHD", "transmission": "string", "engine": "Battery Electric Vehicle (BEV)", "fuelType": ["BEV"], "plant": "SOLIHULL", "featureCodes": ["BF460", "1AF", "022JB", "032DV", "033BV", "033XJ", "020HB", "064JQ", "022LB", "022LC", "022FA", "022GF", "022GH", "022GJ", "043AP", "192AB", "025RM", "025SH", "011BJ", "011AJ", "011AM", "066AH", "087AU", "087AY", "041CZ"], "inventory": [{"ecuAcronym": "TCU", "lastRead": "2023-02-08T08:51:42Z", "effectiveFrom": "2022-12-29T15:52:35Z", "diagnosticVariant": "ev_TCU_L663_2020_00_V1V4", "softwareInventory": [{"swCompId": "f188", "swCompVersion": "L8B2-70712-AAC"}], "hardwareInventory": {"ecuNodeAddr": "1754", "ecuHwPartNo": "B1T3-20C219-AC", "ecuSerialNumber": "SN84389275783"}, "asymKeys": [{"keyLabel": "TCU_ISOOBT_TSTR", "keyType": "CKK_EC", "keySize": 256, "certName": "SDOnboardTester-VTZI_2iaD5c5WQnX5YvnkfFFPMIA_7A9P6-Y3hbZgb4", "certAuthKeyId": "48850fcdc185f9bca074d685a00c797ddb5dc492", "cert": "-----<PERSON><PERSON><PERSON> CERTIFICATE-----\nMIICrDCCAlOgAwIBAgIRAOFjQP+mwZs3CFaZ/3efhMEwCgYIKoZIzj0EAwIwVTEL\nMAkGA1UEBhMCR0IxGjAYBgNVBAoMEUphZ3VhciBMYW5kIFJvdmVyMQswCQYDVQQL\nDAJRQTEdMBsGA1UEAwwUVmVoaWNsZUlzc3VpbmcgU3ViQ0EwHhcNMjQwMzEyMDAx\nMjQ5WhcNMjQxMjMwMDAxMjQ5WjCBlDEWMBQGA1UEBRMNU044NDM4OTI3NTc4MzEL\nMAkGA1UEBhMCR0IxGjAYBgNVBAoMEUphZ3VhciBMYW5kIFJvdmVyMQswCQYDVQQL\nDAJRQTFEMEIGA1UEAww7U0RPbmJvYXJkVGVzdGVyLVZUWklfMmlhRDVjNVdRblg1\nWXZua2ZGRlBNSUFfN0E5UDYtWTNoYlpnYjQwWTATBgcqhkjOPQIBBggqhkjOPQMB\nBwNCAAQmo3lXSHa96vTTgVFLAMYdv81CbQE7fPlfvYYWo6RERzuPGdFZMWrBdxIj\nV0B+2b+EwMKp1LMbQApWaDMR1YaXo4HDMIHAMB0GA1UdDgQWBBR0D0zhysLBIZnH\nOloWiS+s+p0T3zAMBgNVHRMBAf8EAjAAMAsGA1UdDwQEAwIGwDARBgorBgEEAYLm\nHQAABAMEAQcwUAYDVR0gBEkwRzBFBg0rBgEEAYLmHWQBBAEBMDQwMgYIKwYBBQUH\nAgEWJmh0dHBzOi8vcmVwb3NpdG9yeS5qYWd1YXJsYW5kcm92ZXIuY29tMB8GA1Ud\nIwQYMBaAFEiFD83Bhfm8oHTWhaAMeX3bXcSSMAoGCCqGSM49BAMCA0cAMEQCIElr\n4oPd9xwyema9Hz5ANkiXZ1GgxIHpz3Ef+XBG/+HWAiAYJxRrtZgFzBKWFhSKfRV+\nIhzOSZCorog8BmNQ1+Y0Qw==\n-----END CERTIFICATE-----\n"}]}, {"ecuAcronym": "GWM", "lastRead": "2023-02-08T08:51:38Z", "effectiveFrom": "2022-03-29T14:38:50Z", "diagnosticVariant": "ev_GWM_L550_2021_00_V1", "softwareInventory": [{"swCompId": "f124", "swCompVersion": "L8B2-70712-AAC"}, {"swCompId": "f188", "swCompVersion": "L8B2-70712-AAC"}], "hardwareInventory": {"ecuNodeAddr": "1716", "ecuHwPartNo": "H463-82J483-AD", "ecuSerialNumber": "SN99283774502"}, "asymKeys": [{"keyLabel": "GWM_AW_ISOSD_GWSR", "keyType": "CKK_EC", "keySize": 256, "certName": "SDServerGWM-VTZI_2iaD5c5WQnX5YvnkfFFPMIA_7A9P6-Y3hbZgb4", "certAuthKeyId": "48850fcdc185f9bca074d685a00c797ddb5dc492", "cert": "-----<PERSON><PERSON><PERSON> CERTIFICATE-----\nMIIClTCCAjugAwIBAgIQSyCbkuyG3w37VBqgBaHRCDAKBggqhkjOPQQDAjBVMQsw\nCQYDVQQGEwJHQjEaMBgGA1UECgwRSmFndWFyIExhbmQgUm92ZXIxCzAJBgNVBAsM\nAlFBMR0wGwYDVQQDDBRWZWhpY2xlSXNzdWluZyBTdWJDQTAeFw0yNDAzMTIwMDEx\nMTVaFw0yNDEyMzAwMDExMTVaMIGQMRYwFAYDVQQFEw1TTjk5MjgzNzc0NTAyMQsw\nCQYDVQQGEwJHQjEaMBgGA1UECgwRSmFndWFyIExhbmQgUm92ZXIxCzAJBgNVBAsM\nAlFBMUAwPgYDVQQDDDdTRFNlcnZlckdXTS1WVFpJXzJpYUQ1YzVXUW5YNVl2bmtm\nRkZQTUlBXzdBOVA2LVkzaGJaZ2I0MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE\nc6A5MPXo4BcI0sRt7twjEBEaiNtvBid0ASrraffmIdW12miWF7HPuUkV4OL5OkQo\nXYib62gnqg7hKzSnRCufVaOBsDCBrTAdBgNVHQ4EFgQUwjQtYPZmHLQLnL1s0pjB\nQ1jd4xgwDAYDVR0TAQH/BAIwADALBgNVHQ8EBAMCBsAwUAYDVR0gBEkwRzBFBg0r\nBgEEAYLmHWQBBAEBMDQwMgYIKwYBBQUHAgEWJmh0dHBzOi8vcmVwb3NpdG9yeS5q\nYWd1YXJsYW5kcm92ZXIuY29tMB8GA1UdIwQYMBaAFEiFD83Bhfm8oHTWhaAMeX3b\nXcSSMAoGCCqGSM49BAMCA0gAMEUCIQDOQRViZXBbzfMXgSe9grBZAJ4s+1xiHKjI\nMu0LZg9nPgIgCwq7N/CN58CFOvkojrmhM8DOARX+xuEuEcbfmUgIdT8=\n-----END CERTIFICATE-----\n"}], "symKeys": [{"keyType": "msk", "kcv": "c76b52", "generationNo": 1}, {"keyType": "comm", "kcv": "195746", "generationNo": 1}, {"keyLabel": "GWM_AW_JLRVKMS_UPSK", "keyType": "psk", "kcv": "db2b6d", "generationNo": 1}]}]}