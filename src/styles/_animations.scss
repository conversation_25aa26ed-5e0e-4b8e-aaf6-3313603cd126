/*
 * Copyright 2022 (c) Jaguar Land Rover Ltd. All rights reserved.
 */

@use "sass:string";
@use "src/styles/variables" as *;

@mixin slide-in-animation($from, $direction: "vertical", $duration: 0.25s, $fill-mode: none) {
  $transform: translateY;
  @if $direction == "horizontal" {
    $transform: translateX;
  }

  $keyframeName: unique-id();

  // Only works if @keyframe has unique name.
  @keyframes #{$keyframeName} {
    from {
      opacity: 0;
      transform: #{$transform }(#{$from}); // e.g transform: translateY(100px);
    }
    to {
      opacity: 1;
      transform: #{$transform }(0px);
    }
  }

  animation: $keyframeName $duration $fill-mode;
}

@mixin fadein-animation($duration: 0.5s, $fill-mode: forwards) {
  $keyframeName: unique-id();

  @keyframes #{$keyframeName} {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  animation: $keyframeName $duration $fill-mode;
}

@mixin highlight-card-animation($duration: 0.6s, $delay: 0.6s, $fill-mode: forwards) {
  $keyframeName: unique-id();

  @keyframes #{$keyframeName} {
    0% {
      background-color: transparent;
    }
    50% {
      background-color: $grey-tableHeader;
    }
    100% {
      background-color: transparent;
    }
  }

  animation: $keyframeName $duration $delay $fill-mode;
}
