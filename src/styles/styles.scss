/*!
 * Copyright (c) Jaguar Land Rover Ltd 2022. All rights reserved
 */

@use "src/styles/variables" as *;
@use "src/styles/json-pretty" as *;
@use "src/styles/animations" as *;

@import url("https://fonts.googleapis.com/css2?family=Montserrat&display=swap");

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Montserrat", "Segoe UI", "Roboto", "Oxygen",
    Helvetica, Arial, Verdana, Tahoma, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.truncate {
  max-width: 135px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.toggles td {
  padding: 5px;
}

.table-heading th {
  background-color: $grey-tableHeader;
  color: $black;
  position: sticky;
  top: -1px;
}

.toggle-names {
  text-align: right;
  display: table-cell;
  vertical-align: middle;
}

.th-vin {
  width: 24%;
}

.th-control {
  width: 15%;
}

.th-button {
  width: 8%;
}

.error-td {
  padding: 0.5rem 1rem !important;
}

.trBorder {
  border: solid $lightBlue-tableBorder 1px;
  text-align: center;
}

.thBackground {
  background-color: $grey-tableHeader !important;
  color: $black;
  border-width: 1px;
  white-space: nowrap;
}

.tdBorder {
  border-width: 1px !important;
}

.error-tbody {
  border-collapse: collapse;
}

.tableCenterCell {
  text-align: center;
  vertical-align: middle;
}

.errorButton {
  white-space: nowrap;
}

.filterTable > thead > tr > th {
  line-height: 0;
  text-align: left;
}

.filterTable th {
  border-style: hidden !important;
  border-left-style: hidden !important;
  border-right-style: hidden !important;
}

.filterTable td {
  border-style: hidden !important;
  border-left-style: hidden !important;
  border-right-style: hidden !important;
}

.history-table {
  width: 90%;
  margin-left: auto;
  margin-right: auto;
  text-align: center;
}

div.container {
  padding-left: 0px;
  padding-right: 0px;
}

.errorSymbol {
  border: 0;
  background-color: transparent;
}

.scroll-list {
  height: 622px;
  overflow-y: auto;
}

@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(2rem);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.openDeleteMenu {
  animation: fadeIn 0.2s ease-in-out;
}

.modalDeleteConfig {
  display: flex;
  flex-wrap: wrap;
  flex-shrink: 0;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 0;
  border-top: 1px solid $lightBlue-tableBorder;
  border-bottom-right-radius: calc(0.3rem - 1px);
  border-bottom-left-radius: calc(0.3rem - 1px);
}

.boldLabel {
  font-weight: 800;
}

.requiredRed {
  color: $red;
  font-weight: 800;
}

.jsonEditBox {
  border: 1px solid $black;
}

.orPartitionDiv {
  border-bottom: 1px;
  border-color: $black;
  border-bottom-style: solid;
  height: 0.9rem;
  margin: 0.6rem;
  text-align: center;
}

.orSpan {
  background-color: $white;
  font-size: 1rem;
  padding: 1rem;
}

.browseFileInputGrp {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: stretch;
  width: 100%;
}

.noWrap {
  white-space: nowrap;
}

.switchContainer {
  display: flex;
  align-items: center;
  margin-top: 1rem;
}

.switchBtn {
  margin-right: 1rem;
  height: 1.5rem;
}

.form-switch .form-check-input {
  width: 3rem;
}

.description {
  font-size: 0.85rem;
  font-weight: normal;
  color: $gray9;
  padding: 0.1rem 0.75rem;
  margin: 0;
  font-style: italic;
}

.vin-cell {
  position: relative;
  p {
    margin: 0.75rem 0;
  }
  .obg-pill {
    position: absolute;
    top: -75%;
    right: 5%;
    background: transparent;
    font-size: 0.9rem;
  }
  .excluded-pill {
    position: absolute;
    top: -75%;
    left: 5%;
    background: transparent;
    font-size: 0.9rem;
  }
}

.tableSymbol {
  div {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
#layout {
  display: flex;
  overflow: hidden;
}

.na {
  color: #c6c6c6;
}

.channelPermissions {
  font-weight: 600;
  font-style: italic;
}

.btn-check:disabled + .btn,
.btn-check[disabled] + .btn,
.disabled-button:disabled {
  pointer-events: all;
  cursor: not-allowed;
}

.loadingWindow {
  position: absolute;
  top: -2%;
  left: 0%;
  width: 100%;
  height: 100%;
  background: rgba(128, 128, 128, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;
  backdrop-filter: blur(3px);
  z-index: 10;
  text-align: center;
  border-radius: 1rem;
  @include fadein-animation();
}

.scroll-feature-code {
  max-height: 290px;
  overflow-y: auto;
}

.cursor-pointer {
  cursor: pointer;
}

.reset-button-style {
  all: unset;
  display: block;
  width: 100%;
}
