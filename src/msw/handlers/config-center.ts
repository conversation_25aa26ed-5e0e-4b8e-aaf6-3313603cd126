/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

import { http, HttpResponse } from "msw";
import { ENV_CONTEXT } from "utils/constants";

interface RequestBody {
  configurationID: string;
  vins: string[];
}

export const configCenter = [
  // Control - to Config Center
  http.post(`${ENV_CONTEXT.cc_uri}/controls/va`, () => {
    return HttpResponse.json({}, { status: 201 });
  }),

  // Configs that are pulled automatically.
  // Diagnostic dictionary is 1st because it is protected.
  // Then Query is 2nd and Ruleset 3rd as that list is reversed.
  http.get(`${ENV_CONTEXT.cc_uri}/api/v1/config/getmeta`, () => {
    return HttpResponse.json(
      {
        totalPages: 1,
        totalElements: 3,
        pageSize: 10,
        pageNumber: 1,
        data: [
          {
            configurationType: "DIAGNOSTIC_DICTIONARY",
            id: "diag123",
            name: "diag_dictionary",
          },
          {
            configurationType: "RULESET",
            id: "rule123",
            name: "e2eTest1-RS",
          },
          {
            configurationType: "QUERY",
            id: "query123",
            name: "e2eTest1-QF",
          },
        ],
      },
      { status: 200 }
    );
  }),

  // Inspecting a Config.
  // Return value depends on the id.
  http.get<{ id: string }>(`${ENV_CONTEXT.cc_uri}/api/v1/config/getbyid`, ({ request }) => {
    const url = new URL(request.url);
    const id = url.searchParams.get("id");
    if (id === "diag123") {
      return HttpResponse.json(
        {
          configurationType: "DIAGNOSTIC_DICTIONARY",
          file: {
            prop1: { diag: 1 },
            prop2: { diag: 2 },
            prop3: { diag: 3 },
          },
          id: id,
          name: "diag_dictionary",
        },
        { status: 200 }
      );
    } else if (id === "rule123") {
      return HttpResponse.json(
        {
          configurationType: "RULESET",
          file: {
            prop1: { rule: 1 },
            prop2: { rule: 2 },
            prop3: { rule: 3 },
          },
          id: id,
          name: "e2eTest1-RS",
        },
        { status: 200 }
      );
    } else {
      return HttpResponse.json(
        {
          configurationType: "QUERY",
          file: {
            prop1: { query: 1 },
            prop2: { query: 2 },
            prop3: { query: 3 },
          },
          id: id,
          name: "e2eTest1-QF",
        },
        { status: 200 }
      );
    }
  }),
  // Creating Config with ruleset
  http.post(`${ENV_CONTEXT.cc_uri}/api/v1/config/ruleset`, () => {
    return HttpResponse.json(null, { status: 201 });
  }),

  // Creating Config with Signal dictionary
  http.post(`${ENV_CONTEXT.cc_uri}/api/v1/config/signal`, () => {
    return HttpResponse.json(null, { status: 201 });
  }),

  // Creating Config with channel gives error
  http.post(`${ENV_CONTEXT.cc_uri}/api/v1/config/channel`, () => {
    return HttpResponse.json({ message: "Error Creating Configuration" }, { status: 403 });
  }),

  // Config Page - send VIN
  http.post<object, RequestBody>(
    `${ENV_CONTEXT.cc_uri}/api/v1/config/send`,
    async ({ request }) => {
      const { vins } = await request.json();

      if (vins[0] === "errorConfig123456") {
        return HttpResponse.json({ message: "error" }, { status: 403 });
      } else {
        return HttpResponse.json({}, { status: 200 });
      }
    }
  ),

  // Config Page - Update VIN
  http.post<{ id: string }>(`${ENV_CONTEXT.cc_uri}/api/v1/config/updatebyid`, ({ request }) => {
    const url = new URL(request.url);
    const id = url.searchParams.get("id");
    if (id === "query123") {
      return HttpResponse.json({ message: "error" }, { status: 403 });
    } else {
      return HttpResponse.json({}, { status: 200 });
    }
  }),

  // Config Page - Delete Config
  http.delete(`${ENV_CONTEXT.cc_uri}/api/v1/config/deletebyid`, () => {
    return HttpResponse.json({}, { status: 200 });
  }),
];
