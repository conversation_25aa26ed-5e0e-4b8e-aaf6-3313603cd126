/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

import { http, HttpResponse } from "msw";
import { SearchFilter } from "types/data";
import { ENV_CONTEXT } from "utils/constants";

export const configRequestLogger = [
  // Request History Search
  http.post(`${ENV_CONTEXT.crl_uri}/config-log/search`, async ({ request }) => {
    const { requester } = (await request.json()) as SearchFilter;
    if (requester === "errorRequester123") {
      return HttpResponse.json({}, { status: 403 });
    } else if (requester === "noContent12345678") {
      return HttpResponse.text(null, { status: 204 });
    } else {
      return HttpResponse.json(
        [
          {
            activeConfigs: null,
            configurationType: "QUERY",
            created: "2022-01-01",
            id: "1234",
            requester: "johnDoe",
            vins: ["testVinForRequestHistory"],
          },
        ],
        { status: 200 }
      );
    }
  }),
];
