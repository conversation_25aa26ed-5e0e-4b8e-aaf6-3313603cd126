/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

import { http, HttpResponse } from "msw";
import { ENV_CONTEXT } from "utils/constants";

export const dataProductDeliveryV2 = [
  // Is vehicle excluded (from automated DP delivery)
  http.get(`${ENV_CONTEXT.dpd2_uri}/api/excluded-vehicles`, ({ request }) => {
    const vin = request.headers.get("VIN");
    if (vin === "TSTVINBVDNRIG1234") {
        return HttpResponse.json({
            data: [
                {
                    vin: "TSTVINBVDNRIG1234",
                    isExcluded: false,
                },
            ],
        }, { status: 200 });
    }
      return HttpResponse.json({
          data: [
              {
                  vin: "EQUALVINBVDNRIG17",
                  isExcluded: true,
              },
          ],
      }, { status: 200 });
  }),

  //Apply Data Products
  http.put(`${ENV_CONTEXT.dpd2_uri}/api/vehicles/data-products`, () => {
      return HttpResponse.json({ results: [{ vin: "TSTVINBVDNRIG1234", status: "SUCCESS" }] }, { status: 200 });
  }),
];
