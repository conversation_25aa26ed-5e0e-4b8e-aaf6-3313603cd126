/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

import { http, HttpResponse } from "msw";
import { ENV_CONTEXT } from "utils/constants";

export const fleetManagement = [
  http.post(`${ENV_CONTEXT.dv_uri}/v2/vehicles/count`, () => {
      return HttpResponse.json({
          data: {
              count: 3,
          },
      }, { status: 200 });
  }),

  //search endpoint for fleet management
  http.post(`${ENV_CONTEXT.dv_uri}/v2/vehicles/search`, () => {
      return HttpResponse.json({
          data: [
              {
                  identity: {
                      uniqueId: "uid1",
                      identityCreated: "2023-06-01T12:12:12.12Z",
                      vin: "TSTVINBVDNRIG1234",
                      squishVin: "TEST1",
                      vehicleReg: "",
                  },
                  policies: null,
                  assets: null,
                  inventory: null,
                  applications: null,
              },
              {
                  identity: {
                      uniqueId: "uid33",
                      identityCreated: "2023-06-01T12:12:12.12Z",
                      vin: "SALKA9A74NA000087",
                      squishVin: "TEST33",
                      vehicleReg: "",
                  },
                  policies: null,
                  assets: null,
                  inventory: null,
                  applications: null,
              },
          ],
          meta: {
              scrollId: "fac79767-8729-4065-a776-a974bfd60117",
          },
      }, { status: 200 });
  }),

  http.post(`${ENV_CONTEXT.dv_uri}/v2/vehicles/search/scroll`, () => {
      return HttpResponse.json({ data: [], meta: { scrollId: null } }, { status: 200 });
  }),

  http.put(
    `${ENV_CONTEXT.dv_uri}/v2/vehicles/assets/fleetId?identifierType=VIN`,
    () => {
        return HttpResponse.json(null, { status: 200 });
    }
  ),

  http.delete(
    `${ENV_CONTEXT.dv_uri}/v2/vehicles/assets/fleetId?identifierType=VIN`,
    () => {
        return HttpResponse.json(null, { status: 200 });
    }
  ),
];
