/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

import { http, HttpResponse } from "msw";
import { ENV_CONTEXT } from "utils/constants";

export const dataProductManager = [
  // Data Product - Get deltas
  http.get(`${ENV_CONTEXT.dpm_uri}/data-product/delta`, () => {
    return HttpResponse.json(
      [
        {
          comment: "Dp_003 - e2e-test Ashlee Dack",
          createdTimestamp: 1649675677,
          dataProductId: "Dp_003",
          status: "TESTING",
          permissionList: ["L_App_Privacy"],
          queries: [
            {
              comment:
                "<dp> Dp_003 - e2e-test - 2022-May-10 14:26 , elements: 10, queries: 2 , created in Preditor: Beta-Prospect-040 by: agoodman at:  2022-05-10_1426 using dictionary build: 018_2022-03-22_VA-VDCP </dp>, when periodic 5, 1st emit ActualTyrePressureRR",
              emit: ["S00015", "S00060", "S00668"],
              id: "Q9003001",
              out: {
                chId: ["Ch7"],
                eng: 1,
              },
              when: {
                periodic: [5],
              },
              whileThen: [
                {
                  gt: ["S00668", 0],
                },
              ],
            },
            {
              comment: "when periodic 10, 1st emit DTC-IGM-8F",
              emit: ["D00047", "D00049", "D00091", "D00136", "D00453", "D00600", "D00642"],
              id: "Q9003002",
              out: {
                chId: ["Ch5"],
                eng: 0,
              },
              when: {
                periodic: [10],
              },
              whileThen: [
                {
                  eq: ["S00668", 7],
                },
              ],
            },
          ],
          eligibilityCriteria: {
            assets: {
              brand: null,
              modelRange: null,
              modelYear: null,
              fuelType: ["BEV", "PHEV"],
              fleetId: ["MLAP"],
            },
            tags: {
              tags: ["TAG_1", "TAG_2"],
            },
          },
          schemaVersion: "3.0.0",
          version: "1.0.0",
        },
        {
          comment: "Dp_123",
          createdTimestamp: 1649675677,
          dataProductId: "Dp_123",
          status: "TESTING",
          permissionList: ["L_App_Privacy"],
          queries: [
            {
              comment:
                "<dp> Dp_003 - e2e-test - 2022-May-10 14:26 , elements: 10, queries: 2 , created in Preditor: Beta-Prospect-040 by: agoodman at:  2022-05-10_1426 using dictionary build: 018_2022-03-22_VA-VDCP </dp>, when periodic 5, 1st emit ActualTyrePressureRR",
              emit: ["S00015", "S00060", "S00668"],
              id: "Q9003001",
              out: {
                chId: ["Ch7"],
                eng: 1,
              },
              when: {
                periodic: [5],
              },
              whileThen: [
                {
                  gt: ["S00668", 0],
                },
              ],
            },
          ],
          eligibilityCriteria: {
            assets: {
              brand: null,
              modelRange: null,
              modelYear: null,
              fuelType: ["BEV", "PHEV"],
              fleetId: ["MLAP"],
            },
            tags: null,
          },
        },
      ],
      { status: 200 }
    );
  }),

  //Update DP Status
  http.put(`${ENV_CONTEXT.dpm_uri}/data-products/Dp_003/status`, () => {
    return HttpResponse.json("Success", { status: 200 });
  }),

  // Fetch Distinct Tags
  http.get(`${ENV_CONTEXT.dpm_uri}/data-product/tags`, () => {
    return HttpResponse.json(["TAG_1", "TAG_2", "TAG_3"]);
  }),
];
