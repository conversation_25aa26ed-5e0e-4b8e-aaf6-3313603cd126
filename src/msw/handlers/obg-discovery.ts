/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

import { http, HttpResponse } from "msw";
import { ENV_CONTEXT } from "utils/constants";

export const obgDiscovery = [
  // DataProduct Send VIN
  http.get(`${ENV_CONTEXT.obg_uri}/channel-definitions`, () => {
    return HttpResponse.json(
      [
        {
          application: "PS",
          blacklist: false,
          channels: [
            {
              id: "pushSettingsRequest",
              protocol: "MQTT",
              endpoint: "vehicle/{}/ps/data/pushSettingsRequest",
              priority: "Lowest",
              method: "publish"
            }
          ],
          createdBy: "nharten1",
          createdDate: "2024-10-04T07:36:44.831",
          updatedBy: null,
          updatedDate: null,
          permission: null
        },
        {
          application: "RVC",
          blacklist: false,
          channels: [
            {
              id: "setApertureLockStateRequest",
              protocol: "MQTT",
              endpoint: "vcdp/{}/rvc/ctrl/setApertureLockStateRequest",
              priority: "Lowest",
              method: "subscribe"
            },
            {
              id: "setApertureLockStateResponse",
              protocol: "MQTT",
              endpoint: "vehicle/{}/rvc/status/setApertureLockStateResponse",
              priority: "Lowest",
              method: "publish"
            }
          ],
          createdBy: "nharten1",
          createdDate: "2023-10-04T07:36:51.885",
          updatedBy: "asing157",
          updatedDate: "2024-01-22T18:38:45.811",
          permission: ["L_App_Privacy", "Permission_2"]
        }
      ],
      { status: 200 }
    );
  }),

  http.post(`${ENV_CONTEXT.obg_uri}/protocol`, () => {
    return HttpResponse.json("Success", { status: 200 });
  })
];
