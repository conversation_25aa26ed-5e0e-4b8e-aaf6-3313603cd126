/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

import { http, HttpResponse } from "msw";
import { TestRigRequest } from "types/data";
import { PatchVehicle } from "types/patch-vehicle";
import { ENV_CONTEXT } from "utils/constants";

interface RequestBody {
  identifiers: string[];
  inclusions?: string[];
}

const defaultVehicle = {
  identity: {
    uniqueId: "testUniqueId",
    identityCreated: "2023-12-21T12:39:10.294Z",
    vin: "TSTVINBVDNRIG1234",
    squishVin: "string",
    vehicleReg: "ABC 123",
  },
  policies: {
    dataProduct: {
      active: {
        timestamp: "2023-12-21T12:39:10.294Z",
        hash: "string",
        configs: [
          {
            dataProductId: "string",
            modified: "string",
          },
        ],
      },
      preceding: {
        timestamp: "2023-12-21T12:39:10.294Z",
        hash: "string",
        configs: [
          {
            dataProductId: "string",
            modified: "string",
          },
        ],
      },
      pending: {
        timestamp: "2023-12-21T12:39:10.294Z",
        hash: "string",
        configs: [
          {
            dataProductId: "string",
            modified: "string",
          },
        ],
      },
    },
    dictionary: {
      network: {
        active: {
          timestamp: "2023-12-21T12:39:10.294Z",
          version: "string",
        },
        preceding: {
          timestamp: "2023-12-21T12:39:10.294Z",
          version: "string",
        },
        pending: {
          timestamp: "2023-12-21T12:39:10.294Z",
          version: "string",
        },
      },
      diagnostic: {
        active: {
          timestamp: "2023-12-21T12:39:10.294Z",
          version: "string",
        },
        preceding: {
          timestamp: "2023-12-21T12:39:10.294Z",
          version: "string",
        },
        pending: {
          timestamp: "2023-12-21T12:39:10.294Z",
          version: "string",
        },
      },
    },
    vaControls: {
      active: {
        timestamp: "2023-12-21T12:39:10.294Z",
        controls: {
          enableVa: false,
          allowWiFi: true,
          enableMirroring: true,
          enablePushData: false,
        },
      },
      preceding: {
        timestamp: "2023-12-21T12:39:10.294Z",
        controls: {
          enableVa: false,
          allowWiFi: true,
          enableMirroring: true,
          enablePushData: false,
        },
      },
      pending: {
        timestamp: "2023-12-21T12:39:10.294Z",
        controls: {
          enableVa: false,
          allowWiFi: true,
          enableMirroring: true,
          enablePushData: false,
        },
      },
    },
  },
  assets: {
    fleetId: null,
    brand: null,
    modelRange: "X590",
    targetMarket: null,
    soldIntoMarket: "SotaInventorySoldIntoMarket",
    modelYear: "MY19",
    modelName: "I Pace",
    trim: "AWD",
    bodyStyle: "5 DOOR STATION WAGON",
    driver: "RHD",
    transmission: "Automatic",
    engine: "Battery Electric Vehicle (BEV)",
    plant: "GRAZ",
    fuelType: ["BEV"],
    featureCodes: ["002BA", "022BC", "025CT", "025EZ", "025JB", "026BK"],
    vehicleArchitecture: "EVA_2",
  },
  inventory: [
    {
      ecuAcronym: "string",
      diagnosticVariant: "string",
      effectiveFrom: "2023-12-21T12:39:10.294Z",
      lastRead: "2023-12-21T12:39:10.294Z",
      modifiedBy: "string",
      dvSupportedOnboard: true,
      softwareInventory: [
        {
          swCompId: "string",
          swCompVersion: "string",
          swCompType: "string",
        },
      ],
      hardwareInventory: {
        ecuNodeAddr: "testNodeAddress",
        ecuHwPartNo: "string",
        ecuSerialNumber: "string",
      },
    },
  ],
  applications: {
    vehicleAnalytics: {
      status: {
        enableVA: false,
        allowWiFi: true,
        enableMirroring: true,
        enablePushData: false,
        lastVaControlsUpdate: "2023-12-21T12:39:10.294Z",
      },
      manifest: {
        protocolChannels: "string",
        protocolChannelsSchema: "string",
        queries: "string",
        queriesSchema: "string",
        signalDictionary: "testSignalDictionary",
        signalDictionarySchema: "string",
        vaAppSoftwareVersion: "string",
        diagnosticsDictionary: "string",
        diagnosticsDictionarySchema: "string",
        vaConfigAcceptErrorState: "NE",
        lastManifestUpdate: "2023-12-21T12:39:10.294Z",
      },
      errorMessages: [
        {
          faultIdentifier: "string",
          timestamp: "string",
          severity: "string",
          errorMetadata: "string",
        },
      ],
    },
    digitalVehicle: {
      sotaEnabled: true,
      vehicleInSync: true,
      vehicleModifiedDate: "2023-12-21T12:39:10.294Z",
      inventoryModifiedDate: "2023-12-21T12:39:10.294Z",
    },
    obg: {
      isEnabled: true,
    },
  },
  tags: ["TAG_3", "TAG_4", "TAG_5"],
};

export const digitalVehicle = [
  // Control Page - get previous errors
  // Config Page - get manifest data
  // data product - query vins
  http.post<object, RequestBody>(
    `${ENV_CONTEXT.dv_uri}/v2/vehicles/?identifierType=VIN`,
    async ({ request }) => {
      const { identifiers } = await request.json();

      if (identifiers[0] === "NOTFOUND123456789") {
        return HttpResponse.json(
          {
            data: [],
          },
          { status: 200 }
        );
      }

      if (identifiers[0] === "otherError1234567") {
        return HttpResponse.json(
          {
            data: [],
            meta: null,
            errors: [
              {
                message: "something went wrong",
                code: "400",
              },
            ],
          },
          { status: 403 }
        );
      }

      if (identifiers[0] === "EQUALVINBVDNRIG17") {
        return HttpResponse.json(
          {
            data: [
              {
                identity: {
                  uniqueId: "uid33",
                  identityCreated: "2023-06-01T12:12:12.12Z",
                  vin: "EQUALVINBVDNRIG17",
                  squishVin: "TEST33",
                  vehicleReg: "Reg 2",
                },
                policies: null,
                assets: {
                  fleetId: null,
                  brand: null,
                  modelRange: "X590",
                  targetMarket: null,
                  soldIntoMarket: "SotaInventorySoldIntoMarket",
                  modelYear: "MY19",
                  modelName: "I Pace",
                  trim: "AWD",
                  bodyStyle: "5 DOOR STATION WAGON",
                  driver: "RHD",
                  transmission: "Automatic",
                  engine: "Battery Electric Vehicle (BEV)",
                  plant: "GRAZ",
                  fuelType: ["BEV"],
                  featureCodes: ["002BA", "022BC", "025CT", "025EZ", "025JB", "026BK"],
                  vehicleArchitecture: "EVA_2",
                },
                inventory: null,
                applications: {
                  vehicleAnalytics: {
                    status: {
                      enableVA: false,
                      allowWiFi: true,
                      enableMirroring: true,
                      enablePushData: false,
                      lastVaControlsUpdate: "2023-12-21T12:39:10.294Z",
                    },
                    manifest: {
                      protocolChannels: "string",
                      protocolChannelsSchema: "string",
                      queries: "string",
                      queriesSchema: "string",
                      signalDictionary: "testSignalDictionary",
                      signalDictionarySchema: "string",
                      vaAppSoftwareVersion: "string",
                      diagnosticsDictionary: "string",
                      diagnosticsDictionarySchema: "string",
                      vaConfigAcceptErrorState: "NE",
                      lastManifestUpdate: "2023-12-21T12:39:10.294Z",
                    },
                    errorMessages: [
                      {
                        faultIdentifier: "string",
                        timestamp: "string",
                        severity: "string",
                        errorMetadata: "string",
                      },
                    ],
                  },
                },
              },
            ],
          },
          { status: 200 }
        );
      }

      if (identifiers[0] === "EQUALVINBVDNRIG12") {
        return HttpResponse.json(
          {
            data: [
              {
                identity: {
                  uniqueId: "uid1",
                  identityCreated: "2023-06-01T12:12:12.12Z",
                  vin: "TSTVINBVDNRIG1234",
                  squishVin: "TEST1",
                  vehicleReg: "",
                },
                policies: null,
                assets: null,
                inventory: null,
                applications: null,
              },
              {
                identity: {
                  uniqueId: "uid33",
                  identityCreated: "2023-06-01T12:12:12.12Z",
                  vin: "SALKA9A74NA000087",
                  squishVin: "TEST33",
                  vehicleReg: "",
                },
                policies: null,
                assets: null,
                inventory: null,
                applications: {
                  vehicleAnalytics: {
                    status: {
                      enableVA: false,
                      allowWiFi: true,
                      enableMirroring: true,
                      enablePushData: false,
                      lastVaControlsUpdate: "2023-12-21T12:39:10.294Z",
                    },
                    manifest: {
                      protocolChannels: "string",
                      protocolChannelsSchema: "string",
                      queries: "string",
                      queriesSchema: "string",
                      signalDictionary: "testSignalDictionary",
                      signalDictionarySchema: "string",
                      vaAppSoftwareVersion: "string",
                      diagnosticsDictionary: "string",
                      diagnosticsDictionarySchema: "string",
                      vaConfigAcceptErrorState: "NE",
                      lastManifestUpdate: "2023-12-21T12:39:10.294Z",
                    },
                    errorMessages: [
                      {
                        faultIdentifier: "string",
                        timestamp: "string",
                        severity: "string",
                        errorMetadata: "string",
                      },
                    ],
                  },
                },
                tags: ["TAG_3", "TAG_4", "TAG_5"],
              },
            ],
          },
          { status: 200 }
        );
      }

      if (identifiers[0] === "VINNULLMANIFEST12") {
        return HttpResponse.json(
          {
            data: [
              {
                identity: {
                  vin: "VINNULLMANIFEST12",
                  uniqueId: "VINNULLMANIFESTUUID",
                },
                policies: null,
                assets: null,
                inventory: null,
                applications: {
                  vehicleAnalytics: {
                    manifest: null,
                  },
                },
              },
            ],
          },
          { status: 200 }
        );
      }

      if (identifiers[0] === "NULLMANIFESTVALUE") {
        return HttpResponse.json(
          {
            data: [
              {
                identity: {
                  vin: "NULLMANIFESTVALUE",
                  uniqueId: "VINNULLMANIFESTVALUESUUID",
                },
                policies: null,
                assets: null,
                inventory: null,
                applications: {
                  vehicleAnalytics: {
                    manifest: {
                      diagnosticsDictionary: null,
                      protocolChannels: null,
                      queries: null,
                      signalDictionary: null,
                    },
                  },
                },
              },
            ],
          },
          { status: 200 }
        );
      }

      if (identifiers[0] === "ZEROMANIFESTVALUE") {
        return HttpResponse.json(
          {
            data: [
              {
                identity: {
                  vin: "ZEROMANIFESTVALUE",
                  uniqueId: "VINZEROMANIFESTVALUESUUID",
                },
                policies: null,
                assets: null,
                inventory: null,
                applications: {
                  vehicleAnalytics: {
                    manifest: {
                      diagnosticsDictionary: "0.0",
                      protocolChannels: "0.0",
                      queries: "0.0",
                      signalDictionary: "0.0",
                    },
                  },
                },
              },
            ],
          },
          { status: 200 }
        );
      }

      if (identifiers[0] === "FLEETVINBVDNRIG12") {
        return HttpResponse.json(
          {
            data: [
              {
                identity: {
                  uniqueId: "uid2",
                  identityCreated: "2023-06-01T12:12:12.12Z",
                  vin: "SALKA9A74NA000084",
                  squishVin: "TEST2",
                  vehicleReg: "",
                },
                policies: null,
                assets: null,
                inventory: null,
                applications: null,
              },
              {
                identity: {
                  uniqueId: "uid11",
                  identityCreated: "2023-06-01T12:12:12.12Z",
                  vin: "SALKA9A74NA000085",
                  squishVin: "TEST11",
                  vehicleReg: "",
                },
                policies: null,
                assets: null,
                inventory: null,
                applications: null,
              },
              {
                identity: {
                  uniqueId: "uid22",
                  identityCreated: "2023-06-01T12:12:12.12Z",
                  vin: "SALKA9A74NA000086",
                  squishVin: "TEST22",
                  vehicleReg: "",
                },
                policies: null,
                assets: null,
                inventory: null,
                applications: null,
              },
              {
                identity: {
                  uniqueId: "uid33",
                  identityCreated: "2023-06-01T12:12:12.12Z",
                  vin: "SALKA9A74NA000087",
                  squishVin: "TEST33",
                  vehicleReg: "",
                },
                policies: null,
                assets: null,
                inventory: null,
                applications: null,
              },
            ],
          },
          { status: 200 }
        );
      }
      if (identifiers[0] === "TSTAPPLYDPRIG1234") {
        return HttpResponse.json(
          {
            data: [
              {
                identity: {
                  uniqueId: "testUniqueIdApply",
                  identityCreated: "2023-12-21T12:39:10.294Z",
                  vin: "TSTAPPLYDPRIG1234",
                  squishVin: "string",
                  vehicleReg: "string",
                },
                policies: {
                  dataProduct: {
                    active: {
                      timestamp: "2023-12-21T12:39:10.294Z",
                      hash: "string",
                      configs: [
                        {
                          dataProductId: "string",
                          modified: "string",
                        },
                      ],
                    },
                    preceding: {
                      timestamp: "2023-12-21T12:39:10.294Z",
                      hash: "string",
                      configs: [],
                    },
                    pending: {
                      timestamp: "2023-12-21T12:39:10.294Z",
                      hash: "string",
                      configs: [
                        {
                          dataProductId: "Dp_003",
                          modified: "string",
                        },
                      ],
                    },
                  },
                },
                assets: null,
                inventory: null,
                applications: {
                  vehicleAnalytics: {
                    status: {
                      enableVA: false,
                      allowWiFi: true,
                      enableMirroring: true,
                      enablePushData: false,
                      lastVaControlsUpdate: "2023-12-21T12:39:10.294Z",
                    },
                    manifest: {
                      protocolChannels: "string",
                      protocolChannelsSchema: "string",
                      queries: "string",
                      queriesSchema: "string",
                      signalDictionary: "testSignalDictionary",
                      signalDictionarySchema: "string",
                      vaAppSoftwareVersion: "string",
                      diagnosticsDictionary: "string",
                      diagnosticsDictionarySchema: "string",
                      vaConfigAcceptErrorState: "NE",
                      lastManifestUpdate: "2023-12-21T12:39:10.294Z",
                    },
                  },
                  digitalVehicle: {
                    sotaEnabled: true,
                    vehicleInSync: true,
                    vehicleModifiedDate: "2023-12-21T12:39:10.294Z",
                    inventoryModifiedDate: "2023-12-21T12:39:10.294Z",
                  },
                  obg: {
                    isEnabled: true,
                  },
                },
              },
            ],
            meta: {},
            errors: [
              {
                message: "string",
                code: "string",
              },
            ],
          },
          { status: 200 }
        );
      }
      if (identifiers[0] === "TSTREMVEDPWRN1234") {
        return HttpResponse.json(
          {
            data: [
              {
                identity: {
                  uniqueId: "testUniqueIdRemove",
                  identityCreated: "2023-12-21T12:39:10.294Z",
                  vin: "TSTREMVEDPWRN1234",
                  squishVin: "string",
                  vehicleReg: "string",
                },
                policies: {
                  dataProduct: {
                    active: {
                      timestamp: "2023-12-21T12:39:10.294Z",
                      hash: "string",
                      configs: [
                        {
                          dataProductId: "string",
                          modified: "string",
                        },
                      ],
                    },
                    preceding: {
                      timestamp: "2023-12-21T12:39:10.294Z",
                      hash: "string",
                      configs: [],
                    },
                    pending: {
                      timestamp: "2023-12-21T12:39:10.294Z",
                      hash: "string",
                      configs: [
                        {
                          dataProductId: "string",
                          modified: "string",
                        },
                      ],
                    },
                  },
                },
                assets: null,
                inventory: null,
                applications: {
                  vehicleAnalytics: {
                    status: {
                      enableVA: false,
                      allowWiFi: true,
                      enableMirroring: true,
                      enablePushData: false,
                      lastVaControlsUpdate: "2023-12-21T12:39:10.294Z",
                    },
                    manifest: {
                      protocolChannels: "string",
                      protocolChannelsSchema: "string",
                      queries: "string",
                      queriesSchema: "string",
                      signalDictionary: "testSignalDictionary",
                      signalDictionarySchema: "string",
                      vaAppSoftwareVersion: "string",
                      diagnosticsDictionary: "string",
                      diagnosticsDictionarySchema: "string",
                      vaConfigAcceptErrorState: "NE",
                      lastManifestUpdate: "2023-12-21T12:39:10.294Z",
                    },
                  },
                  digitalVehicle: {
                    sotaEnabled: true,
                    vehicleInSync: true,
                    vehicleModifiedDate: "2023-12-21T12:39:10.294Z",
                    inventoryModifiedDate: "2023-12-21T12:39:10.294Z",
                  },
                  obg: {
                    isEnabled: true,
                  },
                },
              },
            ],
            meta: {},
            errors: [
              {
                message: "string",
                code: "string",
              },
            ],
          },
          { status: 200 }
        );
      } else {
        return HttpResponse.json(
          {
            data: [defaultVehicle],
            meta: {},
            errors: [
              {
                message: "string",
                code: "string",
              },
            ],
          },
          { status: 200 }
        );
      }
    }
  ),

  // DataProduct - query-state
  http.post(`${ENV_CONTEXT.dv_uri}/v2/vehicles/policies/dataProduct?identifierType=VIN`, () => {
    return HttpResponse.json(
      {
        data: [
          {
            identifier: "testObject123",
            active: {
              timestamp: "2023-10-10T10:25:40.399Z",
              hash: "string",
              configs: [
                {
                  dataProductId: "1",
                  modified: "2023-10-10T10:25:40.399Z",
                },
                {
                  dataProductId: "2",
                  modified: "2023-10-09T10:25:40.399Z",
                },
                {
                  dataProductId: "3",
                  modified: "2023-10-08T10:25:40.399Z",
                },
              ],
            },
          },
        ],
      },
      { status: 200 }
    );
  }),

  http.patch(`${ENV_CONTEXT.dv_uri}/vehicles/updateVehicle/:vin`, async ({ request, params }) => {
    const { vin } = params;

    if (vin === "TSTVINBVDNRIG1234") {
      const requestBody = (await request.json()) as PatchVehicle;

      if (requestBody.featureCodes?.includes("551EL")) {
        defaultVehicle.assets.featureCodes = requestBody.featureCodes;
        return HttpResponse.json("Success", { status: 200 });
      }
      defaultVehicle.identity.vehicleReg = "Updated vehicleReg";
      return HttpResponse.json("Success", { status: 200 });
    }

    return HttpResponse.json({ message: "Error updating vehicle" }, { status: 400 });
  }),

  // Create Test Rig
  http.post<object, TestRigRequest>(
    `${ENV_CONTEXT.aws_gateway_uri}/createtestrig`,
    async ({ request }) => {
      const { vin, tcu } = await request.json();
      if (vin === "NOTFOUND123456789" && tcu === "VALID_TCU") {
        return HttpResponse.json("Success", { status: 200 });
      }

      return HttpResponse.json({ message: "Error creating rig" }, { status: 403 });
    }
  ),
];
