/*
 * Copyright (c) Jaguar Land Rover Ltd 2022. All rights reserved
 */

import { setupServer } from "msw/node";
import {
  configCenter,
  configRequestLogger,
  dataProductDeliveryV2,
  dataProductManager,
  digitalVehicle,
  obgDiscovery,
} from "./handlers";
import { fleetManagement } from "./handlers/fleet-management";

// This configures a request mocking server with the given request handlers.
export const server = setupServer(
  ...configCenter,
  ...configRequestLogger,
  ...dataProductDeliveryV2,
  ...dataProductManager,
  ...digitalVehicle,
  ...fleetManagement,
  ...obgDiscovery
);
