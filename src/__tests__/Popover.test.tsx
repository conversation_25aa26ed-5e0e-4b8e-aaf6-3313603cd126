/*
 * Copyright 2023 (c) Jaguar Land Rover Ltd. All rights reserved.
 */

import { describe, expect, it } from "vitest";
import { ReactNode } from "react";
import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { Popover } from "../components/Overlays";

const renderHelper = (label: string | ReactNode) => {
  const user = userEvent.setup();
  render(
    <>
      <h1>Outside</h1>
      <Popover label={label}>
        <div>Target</div>
      </Popover>
    </>
  );
  return { user };
};

describe("Popover Component", () => {
  const { getByText, queryByText } = screen;

  it("should display correct popover if label is string", async () => {
    const { user } = renderHelper("String Popover");
    await user.click(getByText("Target"));
    expect(queryByText("String Popover")).toBeInTheDocument();
  });

  it("should display correct popover if label is JSX", async () => {
    const { user } = renderHelper(<h1>Header</h1>);
    await user.click(getByText("Target"));
    expect(queryByText("Header")).toBeInTheDocument();
  });

  it("should hide popover when click outside target", async () => {
    const { user } = renderHelper("Popover12345");
    await user.click(getByText("Target"));
    await user.click(getByText("Outside"));
    expect(queryByText("Popover12345")).not.toBeInTheDocument();
  });

  it("should hide popover when Escape key is pressed", async () => {
    const { user } = renderHelper("Popover10101");
    await user.click(getByText("Target"));
    await user.keyboard("[Escape]");
    expect(queryByText("Popover10101")).not.toBeInTheDocument();
  });

  it("should toggle popover when target is in focus and user presses 'Space' key", async () => {
    const { user } = renderHelper("Popover");

    await user.tab();
    await user.keyboard("[Space]");
    expect(queryByText("Popover")).toBeInTheDocument();

    await user.keyboard("[Space]");
    expect(queryByText("Popover")).not.toBeInTheDocument();
  });

  it("should toggle popover when target is in focus and user presses 'Enter' key", async () => {
    const { user } = renderHelper("Popover");

    await user.tab();
    await user.keyboard("[Enter]");
    expect(queryByText("Popover")).toBeInTheDocument();

    await user.keyboard("[Enter]");
    expect(queryByText("Popover")).not.toBeInTheDocument();
  });
});
