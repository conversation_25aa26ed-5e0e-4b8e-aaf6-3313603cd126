/*
 * Copyright (c) Jaguar Land Rover Ltd 2022. All rights reserved
 */

import { render, screen } from "@testing-library/react";
import { BrowserRouter } from "react-router-dom";
import { describe, expect, test } from "vitest";
import { IdentityDisplay } from "components/DigitalVehicle";
import { Identity, Obg } from "types/data";

const defaultIdentity: Identity = {
  vin: "",
  squishVin: "",
  uniqueId: "",
  identityCreated: "",
  vehicleReg: "",
};

const defaultObg: Obg = {
  isEnabled: true,
};

const renderHelper = (identity?: Identity, obg?: Obg) => {
  render(<BrowserRouter>{IdentityDisplay(identity, obg)}</BrowserRouter>);
};

describe("Navbar Component", () => {
  const { findByText, queryByText } = screen;

  test("does not display with null values", async () => {
    renderHelper();
    const noData = await findByText(/No Identity Data Found/i);
    expect(noData).toBeInTheDocument();
  });

  test("displays when obg is undefined", () => {
    renderHelper(defaultIdentity, undefined);
    const noData = queryByText(/No Identity Data Found/i);
    expect(noData).not.toBeInTheDocument();
  });

  test("displays when identity is undefined", () => {
    renderHelper(undefined, defaultObg);
    const noData = queryByText(/No Identity Data Found/i);
    expect(noData).not.toBeInTheDocument();
  });
});
