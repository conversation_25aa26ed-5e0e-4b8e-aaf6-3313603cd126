/*
 * Copyright (c) Jaguar Land Rover Ltd 2022. All rights reserved
 */

import { <PERSON>rows<PERSON><PERSON>outer as Router } from "react-router-dom";
import { render, screen, waitForElementToBeRemoved } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { describe, expect, test } from "vitest";
import { Control } from "pages/Control";
import { AuthContext, AuthValues } from "context/auth.context";
import { NotificationContextProvider } from "context/notification.context";
import { Notifications } from "components/Notifications";
import { ReactQueryContextProvider } from "context/react-query.context";
import { Role } from "types/permissions";

const renderHelper = (permission: Role = "admin") => {
  const user = userEvent.setup();

  const authValue: AuthValues = {
    firstName: "John",
    lastName: "Doe",
    roles: [permission],
    loginStatus: "checking"
  };

  render(
    <AuthContext.Provider value={authValue}>
      <NotificationContextProvider>
        <ReactQueryContextProvider>
          <Notifications />
          <Router>
            <Control />
          </Router>
        </ReactQueryContextProvider>
      </NotificationContextProvider>
    </AuthContext.Provider>
  );
  return { user };
};

describe("Control Page", () => {
  const {
    getByRole,
    getAllByRole,
    getByPlaceholderText,
    getByText,
    findByText,
    findByRole,
    findAllByAltText,
    findByLabelText,
    queryByRole,
    queryByText
  } = screen;

  describe("Initial load of page", () => {
    test("control toggles are displayed", () => {
      renderHelper();
      const toggleTable = queryByRole("table", { name: "toggle-table" });
      const toggleName = queryByText("Enable VA");
      const toggleButton = queryByRole("radio", { name: /enable va/i });

      expect(toggleTable).toBeInTheDocument();
      expect(toggleName).toBeInTheDocument();
      expect(toggleButton).toBeChecked();
    });

    test("control table is empty", () => {
      renderHelper();

      const controlTable = queryByRole("table", { name: "control-table" });
      const controlHeaders = getAllByRole("columnheader");
      const emptyTable = queryByRole("row", { name: "No VINS Added" });

      expect(controlTable).toBeInTheDocument();
      const headerNames = controlHeaders.map((element) => element.innerHTML);
      expect(headerNames).toEqual([
        expect.any(String),
        "VIN",
        "VA",
        "WiFi",
        "Push",
        "Mirroring",
        ""
      ]);
      expect(emptyTable).toBeInTheDocument();
    });

    test("search bar is empty", () => {
      renderHelper();
      const searchBar = getByPlaceholderText("Enter VIN");
      expect(searchBar).toHaveValue("");
    });
  });

  describe("interacting with enter vin box", () => {
    test("Notification when clicking Add when no vin entered", async () => {
      const { user } = renderHelper();
      const addVinButton = getByRole("button", { name: "Add" });
      user.click(addVinButton);

      const infoNotification = await findByText(/please enter a vin/i);
      expect(infoNotification).toBeInTheDocument();
    });

    test("Notification when trying to add the same VIN twice", async () => {
      const { user } = renderHelper();
      const searchBar = getByPlaceholderText("Enter VIN");
      await user.type(searchBar, "TSTVINBVDNRIG1234");

      await user.click(await findByRole("button", { name: "Add" }));

      const vinTableEntry = await findByRole("cell", { name: /TSTVINBVDNRIG1234/i });
      expect(vinTableEntry).toBeInTheDocument();

      await user.click(await findByRole("button", { name: "Add" }));

      const infoNotification = await findByText(/TSTVINBVDNRIG1234 already exists in list/i);
      expect(infoNotification).toBeInTheDocument();
    });

    test("valid vin entered and added to table", async () => {
      const { user } = renderHelper();
      const searchBar = getByPlaceholderText("Enter VIN");
      await user.type(searchBar, "TSTVINBVDNRIG1234");

      const addVinButton = getByRole("button", { name: "Add" });
      await user.click(addVinButton);

      const vinTableEntry = await findByRole("cell", { name: /TSTVINBVDNRIG1234/i });
      expect(vinTableEntry).toBeInTheDocument();

      const greenLightTableEntry = await findAllByAltText(/green light/i);
      expect(greenLightTableEntry).toHaveLength(2);

      const redLightTableEntry = await findAllByAltText(/red light/i);
      expect(redLightTableEntry).toHaveLength(2);
    });
  });

  describe("interacting with control table", () => {
    test("Success notification when clicking refresh when vin entered", async () => {
      const { user } = renderHelper();
      const searchBar = getByPlaceholderText("Enter VIN");
      await user.type(searchBar, "1234");

      const addVinButton = getByRole("button", { name: "Add" });
      user.click(addVinButton);

      const refreshButton = await findByRole("button", { name: /refresh/i });
      user.click(refreshButton);

      const successNotification = await findByText(/refreshed vin status/i);
      expect(successNotification).toBeInTheDocument();
    });

    test("successful removal of vin from table", async () => {
      const { user } = renderHelper();
      const searchBar = getByPlaceholderText("Enter VIN");
      await user.type(searchBar, "TSTVINBVDNRIG1234");

      const addVinButton = getByRole("button", { name: "Add" });
      await user.click(addVinButton);

      const removeButton = await findByLabelText(/removeVin/i);
      expect(removeButton).toBeInTheDocument();

      user.click(removeButton);
      await waitForElementToBeRemoved(() => queryByRole("cell", { name: /TSTVINBVDNRIG1234/i }));
    });
  });

  describe("interacting with Control Toggle Selector", () => {
    test("no VINs uploaded Notification", async () => {
      const { user } = renderHelper();
      const updateButton = getByRole("button", { name: "Update Vehicles" });
      user.click(updateButton);

      const continueButton = await findByLabelText(/continuebtn/i);
      user.click(continueButton);

      const errorNotification = await findByText(/no vins uploaded/i);
      expect(errorNotification).toBeInTheDocument();
    });

    test("confirmation modal appears, vehicle updated", async () => {
      const { user } = renderHelper();
      const searchBar = getByPlaceholderText("Enter VIN");
      await user.type(searchBar, "TSTVINBVDNRIG1234");

      const addVinButton = getByRole("button", { name: "Add" });
      user.click(addVinButton);

      const removeButton = await findByLabelText(/removeVin/i);
      expect(removeButton).toBeInTheDocument();

      const trueToggles = getAllByRole("radio", { name: /true/i });
      trueToggles.map((toggle) => user.click(toggle));

      const updateButton = getByRole("button", { name: "Update Vehicles" });
      user.click(updateButton);

      const modalMessage = await findByText(/enableva = true/i);
      expect(modalMessage).toBeInTheDocument();

      const sendToVehicleButton = await findByLabelText(/continuebtn/i);
      user.click(sendToVehicleButton);

      const successNotification = await findByText(/controls sent successfully/i);
      expect(successNotification).toBeInTheDocument();
    });

    test("no permission to update, message appears", () => {
      renderHelper("standard");

      const errorMessage = getByText(/You do not have permission to update controls./i);
      const updateButton = getByRole("button", { name: /update vehicles/i });

      expect(errorMessage).toBeInTheDocument();
      expect(updateButton).toBeDisabled();
    });
  });
});
