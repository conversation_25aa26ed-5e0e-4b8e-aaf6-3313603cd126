/*
 * Copyright (c) Jaguar Land Rover Ltd 2022. All rights reserved
 */

import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as Router } from "react-router-dom";
import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { describe, expect, test } from "vitest";
import { RequestHistory } from "pages/RequestHistory";
import { NotificationContextProvider } from "context/notification.context";
import { Notifications } from "components/Notifications";
import { ReactQueryContextProvider } from "context/react-query.context";

const renderHelper = () => {
  const user = userEvent.setup();

  render(
    <NotificationContextProvider>
      <ReactQueryContextProvider>
        <Router>
          <RequestHistory />
        </Router>
        <Notifications />
      </ReactQueryContextProvider>
    </NotificationContextProvider>
  );

  return { user };
};

describe("Request History Page", () => {
  const {
    getByRole,
    getByPlaceholderText,
    findByText,
    findByRole,
    findByLabelText,
    findAllByRole,
  } = screen;

  describe("Initial page load", () => {
    test("filters are updated correctly", async () => {
      const { user } = renderHelper();
      const configTypeInput = getByRole("combobox", { name: /default select example/i });
      await user.selectOptions(configTypeInput, "Query");

      const query = getByRole("option", { name: "Query" }) as HTMLOptionElement;
      expect(query.selected).toBe(true);

      const vinInput = getByPlaceholderText(/enter vin/i);
      await user.type(vinInput, "testVin");
      expect(vinInput).toHaveValue("testVin");

      const requesterNameInput = getByPlaceholderText(/enter requester name/i);
      await user.type(requesterNameInput, "johnDoe");
      expect(requesterNameInput).toHaveValue("johnDoe");

      const startDateInput = getByPlaceholderText(/date from/i);
      await user.click(startDateInput);
      await user.type(startDateInput, "2020-01-01");
      expect(startDateInput).toHaveValue("2020-01-01");

      const endDateInput = getByPlaceholderText(/date to/i);
      await user.click(endDateInput);
      await user.type(endDateInput, "2022-01-01");
      expect(endDateInput).toHaveValue("2022-01-01");
    });
  });

  describe("returning data", () => {
    test("getting data using requester", async () => {
      const { user } = renderHelper();
      const requesterNameInput = getByPlaceholderText(/enter requester name/i);
      await user.type(requesterNameInput, "johnDoe");

      const searchButton = getByRole("button", { name: "Search Requests" });
      await user.click(searchButton);

      const requester = await findByRole("cell", { name: /johndoe/i });
      expect(requester).toBeInTheDocument();
    });

    test("pagination buttons working", async () => {
      renderHelper();

      const showAllButton = await findByRole("button", { name: /show all/i });
      expect(showAllButton).toBeInTheDocument();

      const prevButton = await findByRole("button", { name: /previous/i });
      expect(prevButton).toBeInTheDocument();
      expect(prevButton).toBeDisabled();

      const nextButton = await findByRole("button", { name: /next/i });
      expect(nextButton).toBeInTheDocument();
    });

    test("Notification when no requester inputted", async () => {
      const { user } = renderHelper();
      const searchButton = getByRole("button", { name: "Search Requests" });
      user.click(searchButton);

      const infoNotification = await findByText(/please enter a requester name/i);
      expect(infoNotification).toBeInTheDocument();
    });

    test("Notification when error requester inputted", async () => {
      const { user } = renderHelper();
      const requesterNameInput = getByPlaceholderText(/enter requester name/i);
      await user.type(requesterNameInput, "errorRequester123");

      const searchButton = getByRole("button", { name: "Search Requests" });
      await user.click(searchButton);

      const infoNotification = await findByText(/Forbidden/i);
      expect(infoNotification).toBeInTheDocument();
    });

    test("Notification when no content available", async () => {
      const { user } = renderHelper();
      const requesterNameInput = getByPlaceholderText(/enter requester name/i);
      await user.type(requesterNameInput, "noContent12345678");

      const searchButton = getByRole("button", { name: "Search Requests" });
      await user.click(searchButton);

      const infoNotification = await findByText(/No Content Available For Filters Selected/i);
      expect(infoNotification).toBeInTheDocument();
    });

    test("clicking view config and vehicles data", async () => {
      const { user } = renderHelper();
      const requesterNameInput = getByPlaceholderText(/enter requester name/i);
      await user.type(requesterNameInput, "johnDoe");
      expect(requesterNameInput).toHaveValue("johnDoe");

      const searchButton = getByRole("button", { name: "Search Requests" });
      user.click(searchButton);

      const viewVehicles = await findAllByRole("button", { name: /view/i });
      user.click(viewVehicles[1]);

      const closeButton = await findByLabelText(/close/i);
      expect(closeButton).toBeInTheDocument();

      const vin = await findByText(/testVinForRequestHistory/i);
      expect(vin).toBeInTheDocument();
    });
  });
});
