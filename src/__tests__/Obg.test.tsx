/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as Router } from "react-router-dom";
import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { describe, expect, it } from "vitest";
import { NotificationContextProvider } from "context/notification.context";
import { Notifications } from "components/Notifications";
import { Obg } from "pages/Obg";
import { ReactQueryContextProvider } from "context/react-query.context";

const renderHelper = () => {
  const user = userEvent.setup();

  render(
    <ReactQueryContextProvider>
      <NotificationContextProvider>
        <Router>
          <Obg />
        </Router>
        <Notifications />
      </NotificationContextProvider>
    </ReactQueryContextProvider>
  );
  return { user };
};

describe("OBG Discovery Page", () => {
  const { queryByText, getByRole, findByText, findAllByRole } = screen;

  it("form contents are displayed", () => {
    renderHelper();
    const vehicleInput = queryByText("Enter a Vehicle Id");
    expect(vehicleInput).toBeInTheDocument();

    const jsonInput = queryByText("Enter a valid JSON Object");
    expect(jsonInput).toBeInTheDocument();

    const buttonSearch = getByRole("button", { name: /Submit/i });
    expect(buttonSearch).toBeInTheDocument();
  });

  it("form inputs are as expected", async () => {
    const { user } = renderHelper();
    const vehicleInput = getByRole("textbox", { name: /Enter a Vehicle Id/i });
    await user.type(vehicleInput, "1234");

    const jsonInput = getByRole("textbox", { name: /Enter a valid JSON Object/i });
    await user.type(jsonInput, JSON.stringify("{{'textBox': 1}"));

    expect(vehicleInput).toHaveValue("1234");
    expect(jsonInput).toHaveValue(JSON.stringify("{'textBox': 1}"));
  });

  it("form submit are as expected", async () => {
    const { user } = renderHelper();
    const vehicleInput = getByRole("textbox", { name: /Enter a Vehicle Id/i });
    await user.type(vehicleInput, "1234");

    const jsonInput = getByRole("textbox", { name: /Enter a valid JSON Object/i });
    await user.type(jsonInput, JSON.stringify("{{'textBox': 1}"));

    const buttonSearch = getByRole("button", { name: /Submit/i });
    await user.click(buttonSearch);

    expect(await findByText("Protocol created successfully")).toBeInTheDocument();
  });

  it("form submit does not submit if all values not entered", async () => {
    const { user } = renderHelper();
    const vehicleInput = getByRole("textbox", { name: /Enter a Vehicle Id/i });
    await user.type(vehicleInput, "1234");

    const buttonSearch = getByRole("button", { name: /Submit/i });
    await user.click(buttonSearch);

    expect(await findByText("This field is required")).toBeInTheDocument();
  });

  it("should display channel definitions on load", async () => {
    const { user } = renderHelper();
    const psApplication = await findByText("PS");
    const rvcApplicaion = await findByText("RVC");

    const permissions = await findByText("L_App_Privacy");
    await user.hover(permissions);
    const permissionsTooltip = await findByText("Permission_2");

    expect(psApplication).toBeInTheDocument();
    expect(rvcApplicaion).toBeInTheDocument();
    expect(permissionsTooltip).toBeInTheDocument();
  });

  it("should display channels on inspect click", async () => {
    const { user } = renderHelper();
    const inspectButton = await findAllByRole("button", { name: /inspect/i });
    await user.click(inspectButton[0]);
    const channelEndpoint = await findByText("vehicle/{}/ps/data/pushSettingsRequest");
    const channelID = await findByText("pushSettingsRequest");

    expect(channelEndpoint).toBeInTheDocument();
    expect(channelID).toBeInTheDocument();
  });
});
