/*
 * Copyright 2023 (c) Jaguar Land Rover Ltd. All rights reserved.
 */

import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { describe, expect, it, vi } from "vitest";
import { Notifications } from "components/Notifications";
import { Notification, NotificationContext } from "context/notification.context";

const renderHelper = (expandOption = false, expandContent: (string | null)[] | string = "") => {
  vi.spyOn(globalThis, "setTimeout");
  vi.spyOn(globalThis, "clearTimeout");
  const remove = vi.fn();
  const user = userEvent.setup();
  const notificationCotextValues = {
    notifications: [
      {
        id: "123",
        type: "default" as Notification["type"],
        title: "INFO",
        body: "5 rows inserted",
        expandOption,
        expandContent,
      },
    ],
    remove,
    notify: () => undefined,
  };
  render(
    <NotificationContext.Provider value={notificationCotextValues}>
      <Notifications />
    </NotificationContext.Provider>
  );
  return { user, remove };
};

describe("Notifications Component", () => {
  const { queryByText, getByLabelText, getByRole, getByText, findByText } = screen;

  it("should render with correct type, title and body text", () => {
    renderHelper();
    expect(queryByText("INFO")).toBeInTheDocument();
    expect(queryByText("5 rows inserted")).toBeInTheDocument();
  });

  it("should evoke 'remove' method when close button is clicked", async () => {
    const { user, remove } = renderHelper();
    const closeButton = getByLabelText("Close Notification");
    await user.click(closeButton);
    expect(remove).toHaveBeenCalledWith("123");
  });

  it("should clear clearTimeout on mouseEnter", async () => {
    const { user } = renderHelper();
    const notificationItem = getByRole("log");
    user.hover(notificationItem);
    expect(clearTimeout).not.toHaveBeenCalled();
  });

  it("should evoke setTimeout on mouseLeave", async () => {
    const { user } = renderHelper();
    const notificationItem = getByRole("log");
    await user.unhover(notificationItem);
    expect(setTimeout).toHaveBeenCalled();
  });

  it("should expand notification string if expand button clicked", async () => {
    const { user } = renderHelper(true, "HELLO");
    const expandButton = getByText("Expand");

    user.click(expandButton);
    expect(await findByText(/HELLO/i)).toBeInTheDocument();
  });

  it("should expand notification array if expand button clicked", async () => {
    const { user } = renderHelper(true, ["HELLO"]);
    const expandButton = getByText("Expand");

    user.click(expandButton);
    expect(await findByText(/HELLO/i)).toBeInTheDocument();
  });
});
