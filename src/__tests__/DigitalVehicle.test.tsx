/*
 * Copyright (c) Jaguar Land Rover Ltd 2022. All rights reserved
 */

import { Browser<PERSON>outer as Router } from "react-router-dom";
import { act, render, screen, waitFor } from "@testing-library/react";
import { userEvent } from "@testing-library/user-event";
import { describe, expect, test } from "vitest";
import { DigitalVehicle } from "pages/DigitalVehicle";
import { NotificationContextProvider } from "context/notification.context";
import { Notifications } from "components/Notifications";
import { ReactQueryContextProvider } from "context/react-query.context";
import { AuthContext, AuthValues } from "context/auth.context";

const authValue: AuthValues = {
  firstName: "John",
  lastName: "Doe",
  roles: ["admin"],
  loginStatus: "checking",
};

const renderHelper = () => {
  const user = userEvent.setup();

  act(() => {
    render(
      <AuthContext.Provider value={authValue}>
        <NotificationContextProvider>
          <ReactQueryContextProvider>
            <Router>
              <DigitalVehicle />
            </Router>
          </ReactQueryContextProvider>
          <Notifications />
        </NotificationContextProvider>
      </AuthContext.Provider>
    );
  });
  return { user };
};

describe("Digital Vehicle Page", () => {
  const {
    getByRole,
    getByPlaceholderText,
    findByText,
    findAllByText,
    queryByRole,
    getAllByRole,
    findByRole,
    findByPlaceholderText,
  } = screen;

  test("search bar is displayed", () => {
    renderHelper();
    const buttonSearch = getByRole("button", { name: /Search/i });
    expect(buttonSearch).toBeInTheDocument();

    const searchBar = getByPlaceholderText(/enter vin/i);
    expect(searchBar).toBeInTheDocument();
  });

  test("manifest, assets and ecu data is displayed", async () => {
    const { user } = renderHelper();
    const searchBar = getByPlaceholderText(/enter vin/i);
    await user.type(searchBar, "TSTVINBVDNRIG1234");

    const buttonSearch = getByRole("button", { name: /Search/i });
    await user.click(buttonSearch);

    const vehicleDetails = await findByText(/testUniqueId/i);
    expect(vehicleDetails).toBeInTheDocument();

    const control = await findAllByText(/true/i);
    expect(control).toHaveLength(2);

    const manifest = await findByText(/testSignalDictionary/i);
    expect(manifest).toBeInTheDocument();

    const featureCodeAsset = await findByText(/002BA/i);
    expect(featureCodeAsset).toBeInTheDocument();

    const modelRangeAsset = await findByText(/X590/i);
    expect(modelRangeAsset).toBeInTheDocument();

    const ecu = await findByText(/ecu components/i);
    await user.click(ecu);
    const ecuNodeAddress = await findByText(/testNodeAddress/i);
    expect(ecuNodeAddress).toBeInTheDocument();
  });

  test("Notification when no vin entered", async () => {
    const { user } = renderHelper();
    const buttonSearch = getByRole("button", { name: /Search/i });
    await user.click(buttonSearch);

    const infoNotification = await findByText(/please enter a vin/i);
    expect(infoNotification).toBeInTheDocument();
  });

  test("Update OBG data is not initially displayed", async () => {
    const obgCard = queryByRole("paragraph", { name: "Change Vehicle Type to OBG or OBA" });
    expect(obgCard).not.toBeInTheDocument();
  });

  test("Update OBG data is displayed after entering vin", async () => {
    const { user } = renderHelper();
    const searchBar = getByPlaceholderText(/enter vin/i);
    await user.type(searchBar, "TSTVINBVDNRIG1234");

    const buttonSearch = getByRole("button", { name: /Search/i });
    await user.click(buttonSearch);

    const updateTypeAccordian = await findByText(/update vehicle type/i);
    await user.click(updateTypeAccordian);

    const obgCard = await findByText("Change Vehicle Type to OBG or OBA");
    expect(obgCard).toBeInTheDocument();
  });

  test("Update VehicleTag data is not initially displayed", async () => {
    const vehicleTagCard = queryByRole("paragraph", {
      name: "Add or Remove Vehicle Tags To Active Group",
    });
    expect(vehicleTagCard).not.toBeInTheDocument();
  });

  test("Update VehicleTag data is displayed after entering vin", async () => {
    const { user } = renderHelper();
    await user.type(getByPlaceholderText(/enter vin/i), "TSTVINBVDNRIG1234");
    await user.click(getByRole("button", { name: /Search/i }));

    const updateTagsAccordian = await findByText(/update tags/i);
    await user.click(updateTagsAccordian);

    expect(await findByText("Add or Remove Vehicle Tags To Active Group")).toBeInTheDocument();
  });

  test("VehicleTag buttons are displayed but disabled", async () => {
    const { user } = renderHelper();
    await user.type(getByPlaceholderText(/enter vin/i), "TSTVINBVDNRIG1234");
    await user.click(getByRole("button", { name: /Search/i }));

    const updateTagsAccordian = await findByText(/update tags/i);
    await user.click(updateTagsAccordian);

    const buttonUpdate = await waitFor(() => getByRole("button", { name: /Reset/i }));
    expect(buttonUpdate).toBeDisabled();

    const buttonReset = getByRole("button", { name: /Reset/i });
    expect(buttonReset).toBeInTheDocument();
    expect(buttonReset).toBeDisabled();
  });

  test("VehicleTag tag buttons transfer when clicked, and enable reset and update buttons", async () => {
    const { user } = renderHelper();
    await user.type(getByPlaceholderText(/enter vin/i), "TSTVINBVDNRIG1234");
    await user.click(getByRole("button", { name: /Search/i }));

    const updateTagsAccordian = await findByText(/update tags/i);
    await user.click(updateTagsAccordian);

    const testTag1 = await waitFor(() => getByRole("button", { name: /TAG_1/i }));
    expect(testTag1).toBeInTheDocument();

    expect(screen.getByTestId("AvailableTagDiv")).toContainElement(testTag1);
    await user.click(testTag1);

    expect(getByRole("button", { name: /Update/i })).toBeEnabled();
    expect(getByRole("button", { name: /Reset/i })).toBeEnabled();
  });

  test("VehicleTag distinct tags are not populated when present in active tags", async () => {
    const { user } = renderHelper();
    await user.type(getByPlaceholderText(/enter vin/i), "TSTVINBVDNRIG1234");
    await user.click(getByRole("button", { name: /Search/i }));

    const updateTagsAccordian = await findByText(/update tags/i);
    await user.click(updateTagsAccordian);

    const testTag3 = await waitFor(() => getByRole("button", { name: /TAG_3/i }));
    expect(testTag3).toBeInTheDocument();

    expect(screen.getByTestId("AvailableTagDiv")).not.toContainElement(testTag3);
    expect(screen.getByTestId("ActiveTagDiv")).toContainElement(testTag3);
  });

  test("vehicle profile - is displayed after entering vin", async () => {
    const { user } = renderHelper();
    await user.type(getByPlaceholderText(/enter vin/i), "TSTVINBVDNRIG1234");
    await user.click(getByRole("button", { name: /Search/i }));

    const addProfileAccordian = await findByText(/add vehicle profile/i);
    await user.click(addProfileAccordian);

    expect(await findByText("Update the vehicle to a predefined template")).toBeInTheDocument();
  });

  test("vehicle profile - disabled until profile selected", async () => {
    const { user } = renderHelper();
    await user.type(getByPlaceholderText(/enter vin/i), "TSTVINBVDNRIG1234");
    await user.click(getByRole("button", { name: /Search/i }));

    const addProfileAccordian = await findByText(/add vehicle profile/i);
    await user.click(addProfileAccordian);

    const button = getByRole("button", { name: /submit/i });
    expect(button).toBeDisabled();
  });

  test("vehicle profile - vehicle updated on submit", async () => {
    const { user } = renderHelper();
    await user.type(getByPlaceholderText(/enter vin/i), "TSTVINBVDNRIG1234");
    await user.click(getByRole("button", { name: /Search/i }));

    const initialProfileReg = await findByText(/ABC 123/i);
    expect(initialProfileReg).toBeInTheDocument();

    const addProfileAccordian = await findByText(/add vehicle profile/i);
    await user.click(addProfileAccordian);

    const select = getByRole("combobox");
    await user.selectOptions(select, "L460");

    const submitButton = getByRole("button", { name: /submit/i });
    await user.click(submitButton);

    const successNotification = await findByText(/L460 Profile Added/i);
    expect(successNotification).toBeInTheDocument();

    const updatedProfileReg = await findByText(/Updated vehicleReg/i);
    expect(updatedProfileReg).toBeInTheDocument();
  });

  test("vehicle profile - shows error notification when fail to patch", async () => {
    const { user } = renderHelper();
    await user.type(getByPlaceholderText(/enter vin/i), "EQUALVINBVDNRIG17");
    await user.click(getByRole("button", { name: /Search/i }));

    const initialProfileReg = await findByText(/Reg 2/i);
    expect(initialProfileReg).toBeInTheDocument();

    const addProfileAccordian = await findByText(/add vehicle profile/i);
    await user.click(addProfileAccordian);

    const select = getByRole("combobox");
    await user.selectOptions(select, "L460");

    const submitButton = getByRole("button", { name: /submit/i });
    await user.click(submitButton);

    const errorNotification = await findByText(/Error updating vehicle profile/i);
    expect(errorNotification).toBeInTheDocument();

    //vehicle remains the same as before
    expect(initialProfileReg).toBeInTheDocument();
  });

  test("create rig - is displayed selecting tab", async () => {
    const { user } = renderHelper();

    // Click tab
    const createRigTab = getByRole("tab", { name: /Create Rig/i });
    await user.click(createRigTab);

    // Find "Add a Rig" card
    const createRigCard = await findByText(/add a rig/i);
    expect(createRigCard).toBeInTheDocument();
  });

  test("create rig - button disabled when all fields not filled", async () => {
    const { user } = renderHelper();

    const createRigTab = getByRole("tab", { name: /Create Rig/i });
    await user.click(createRigTab);

    // Check create is initially disabled
    const createButton = getByRole("button", { name: /Create Rig/i });
    expect(createButton).toBeDisabled();

    // Enter VIN, thencheck again Create is still disabled
    await user.type(getAllByRole("textbox")[0], "TSTVINBVDNRIG1234");
    expect(createButton).toBeDisabled();
  });

  test("create rig - error when vin is not valid (17 chars, alphanumerical)", async () => {
    const { user } = renderHelper();

    const createRigTab = getByRole("tab", { name: /Create Rig/i });
    await user.click(createRigTab);

    // Enter all fields to enable create button, with invalid vin
    await user.type(getAllByRole("textbox")[0], "INVALIDVIN");
    await user.type(getAllByRole("textbox")[1], "TCU");
    await user.selectOptions(getByRole("combobox"), "ENG");

    // click create and notification appears
    const createButton = getByRole("button", { name: /Create Rig/i });
    await user.click(createButton);
    const errorNotification = await findByText(/INVALIDVIN is not a valid VIN/i);
    expect(errorNotification).toBeInTheDocument();
  });

  test("create rig - error when vin already exists", async () => {
    const { user } = renderHelper();

    const createRigTab = getByRole("tab", { name: /Create Rig/i });
    await user.click(createRigTab);

    // Enter all fields to enable create button, with pre-existing vin
    await user.type(getAllByRole("textbox")[0], "TSTVINBVDNRIG1234");
    await user.type(getAllByRole("textbox")[1], "TCU");
    await user.selectOptions(getByRole("combobox"), "ENG");

    // click create and notification appears
    const createButton = getByRole("button", { name: /Create Rig/i });
    await user.click(createButton);
    const errorNotification = await findByText(
      /Vehicle with VIN TSTVINBVDNRIG1234 already exists./i
    );
    expect(errorNotification).toBeInTheDocument();
  });

  test("create rig - success creating rig", async () => {
    const { user } = renderHelper();

    const createRigTab = getByRole("tab", { name: /Create Rig/i });
    await user.click(createRigTab);

    // Enter all fields with valid vin
    await user.type(getAllByRole("textbox")[0], "NOTFOUND123456789");
    await user.type(getAllByRole("textbox")[1], "VALID_TCU");
    await user.selectOptions(getByRole("combobox"), "ENG");

    // click create and test rig created successfully
    const createButton = getByRole("button", { name: /Create Rig/i });
    await user.click(createButton);
    const successNotification = await findByText(/Test Rig Created/i);
    expect(successNotification).toBeInTheDocument();
  });

  test("create rig - error creating rig", async () => {
    const { user } = renderHelper();

    const createRigTab = getByRole("tab", { name: /Create Rig/i });
    await user.click(createRigTab);

    // Enter all fields
    await user.type(getAllByRole("textbox")[0], "NOTFOUND123456789");
    await user.type(getAllByRole("textbox")[1], "ERROR_TCU");
    await user.selectOptions(getByRole("combobox"), "ENG");

    // click create, problem on back-end, error notification
    const createButton = getByRole("button", { name: /Create Rig/i });
    await user.click(createButton);
    const errorNotification = await findByText(/Error Creating Test Rig/i);
    expect(errorNotification).toBeInTheDocument();
  });

  test("feature codes - edit button is displayed after entering vin + modal opens", async () => {
    const { user } = renderHelper();
    const intiialEditButton = queryByRole("button", { name: /Edit/i });
    expect(intiialEditButton).not.toBeInTheDocument();

    await user.type(getByPlaceholderText(/enter vin/i), "TSTVINBVDNRIG1234");
    await user.click(getByRole("button", { name: /Search/i }));

    const editButton = await findByRole("button", { name: /EDIT/i });
    await user.click(editButton);

    expect(await findByText("Update Feature Codes for TSTVINBVDNRIG1234")).toBeInTheDocument();
  });

  test("feature codes - codes are displayed in current and available lists", async () => {
    const { user } = renderHelper();
    await user.type(getByPlaceholderText(/enter vin/i), "TSTVINBVDNRIG1234");
    await user.click(getByRole("button", { name: /Search/i }));

    const editButton = await findByRole("button", { name: /EDIT/i });
    await user.click(editButton);

    const currentCodes = await findByText("Current Codes (6)", { selector: ".modal.show li" });
    const currentCode = await findByText(/002BA/i, { selector: ".modal.show li" });
    const availableCode = await findByText("551EL");

    expect(currentCodes).toBeInTheDocument();
    expect(currentCode).toBeInTheDocument();
    expect(availableCode).toBeInTheDocument();
  });

  test("feature codes - current codes are searchable", async () => {
    const { user } = renderHelper();
    await user.type(getByPlaceholderText(/enter vin/i), "TSTVINBVDNRIG1234");
    await user.click(getByRole("button", { name: /Search/i }));

    const editButton = await findByRole("button", { name: /EDIT/i });
    await user.click(editButton);

    const currentSearch = await findByPlaceholderText(/search current/i);
    await user.type(currentSearch, "002BA");
    const currentCodes = await findByText("Current Codes (1)", { selector: ".modal.show li" });

    expect(currentCodes).toBeInTheDocument();
  });

  test("feature codes - available codes are searchable", async () => {
    const { user } = renderHelper();
    await user.type(getByPlaceholderText(/enter vin/i), "TSTVINBVDNRIG1234");
    await user.click(getByRole("button", { name: /Search/i }));

    const editButton = await findByRole("button", { name: /EDIT/i });
    await user.click(editButton);

    const currentSearch = await findByPlaceholderText(/search available/i);
    await user.type(currentSearch, "551EL");
    const currentCodes = await findByText("Available Codes (1)", { selector: ".modal.show li" });

    expect(currentCodes).toBeInTheDocument();
  });

  test("feature codes - codes are updated", async () => {
    const { user } = renderHelper();
    await user.type(getByPlaceholderText(/enter vin/i), "TSTVINBVDNRIG1234");
    await user.click(getByRole("button", { name: /Search/i }));

    const editButton = await findByRole("button", { name: /EDIT/i });
    await user.click(editButton);

    // click on one code to remove and add
    const currentCode = await findByText(/002BA/i, { selector: ".modal.show li" });
    const availableCode = await findByText("551EL");
    await user.click(currentCode);
    await user.click(availableCode);

    // confirmation is diplayed
    const alert = await findByRole("alert");
    expect(alert).toHaveTextContent("Remove: 002BA");
    expect(alert).toHaveTextContent("Add: 551EL");

    const saveButton = await findByRole("button", { name: /save changes/i });
    await user.click(saveButton);

    const successNotification = await findByText(/Feature Codes Updated/i);
    expect(successNotification).toBeInTheDocument();

    // Code displayed on updated list and rendered in modal
    const updatedAddedCodes = await findAllByText("551EL");
    expect(updatedAddedCodes).toHaveLength(2);

    // only code from modal remains, no longer in list
    const updatedremovedCodes = await findAllByText("002BA");
    expect(updatedremovedCodes).toHaveLength(1);
  });

  test("feature codes - shows error notification when fail to patch ", async () => {
    const { user } = renderHelper();
    await user.type(getByPlaceholderText(/enter vin/i), "EQUALVINBVDNRIG17");
    await user.click(getByRole("button", { name: /Search/i }));

    const editButton = await findByRole("button", { name: /EDIT/i });
    await user.click(editButton);

    const currentCode = await findByText(/002BA/i, { selector: ".modal.show li" });
    const availableCode = await findByText("551EL");

    await user.click(currentCode);
    await user.click(availableCode);

    const saveButton = await findByRole("button", { name: /save changes/i });
    await user.click(saveButton);

    const successNotification = await findByText(/Error updating feature codes/i);
    expect(successNotification).toBeInTheDocument();
  });

  test("feature codes - clear selection button resets state", async () => {
    const { user } = renderHelper();
    await user.type(getByPlaceholderText(/enter vin/i), "TSTVINBVDNRIG1234");
    await user.click(getByRole("button", { name: /Search/i }));

    const editButton = await findByRole("button", { name: /EDIT/i });
    await user.click(editButton);

    const currentCode = await findByText(/022BC/i, { selector: ".modal.show li" });
    const availableCode = await findByText("551EM");
    await user.click(currentCode);
    await user.click(availableCode);

    const alert = await findByRole("alert");
    expect(alert).toHaveTextContent("Remove: 022BCAdd: 551EM");

    const clearButton = await findByRole("button", { name: /clear selection/i });
    await user.click(clearButton);

    const alertAfterCLear = queryByRole("alert");
    expect(alertAfterCLear).not.toBeInTheDocument();
  });
});
