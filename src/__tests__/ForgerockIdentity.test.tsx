/*
 * Copyright (c) Jaguar Land Rover Ltd 2022. All rights reserved
 */

import { Browser<PERSON>outer as Router } from "react-router-dom";
import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { describe, expect, test } from "vitest";
import { NotificationContextProvider } from "context/notification.context";
import { Notifications } from "components/Notifications";
import { ForgeRockIdentity } from "../pages/ForgerockIdentity";

const renderHelper = () => {
  const user = userEvent.setup();

  render(
    <NotificationContextProvider>
      <Router>
        <ForgeRockIdentity />
      </Router>
      <Notifications />
    </NotificationContextProvider>
  );
  return { user };
};

describe("Forgerock Identity Page", () => {
  const { getByRole } = screen;

  test("search bar is displayed", () => {
    renderHelper();
    const buttonSearch = getByRole("button", { name: /Search/i });
    expect(buttonSearch).toBeInTheDocument();

    const searchBar = getByRole("textbox", { name: /Enter VIN/i });
    expect(searchBar).toBeInTheDocument();
  });
});
