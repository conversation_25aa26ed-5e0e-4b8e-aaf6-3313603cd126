/*
 * Copyright 2024 (c) Jaguar Land Rover Ltd. All rights reserved.
 */

import { render, screen } from "@testing-library/react";
import { describe, expect, it, vi } from "vitest";
import userEvent from "@testing-library/user-event";
import { TextArea } from "components/Form";

const renderHelper = (label = "", error = "") => {
  const user = userEvent.setup();
  const onChange = vi.fn();
  render(<TextArea error={error} label={label} onChange={onChange} value="vin123" />);
  return { user, onChange };
};

describe("TextArea Component", () => {
  const { queryByDisplayValue, queryByText, getByDisplayValue } = screen;

  it("it should display correct value, label and error message", () => {
    renderHelper("VIN", "incorrect value");

    const value = queryByDisplayValue("vin123");
    const label = queryByText("VIN");
    const error = queryByText("incorrect value");

    expect(value).toBeInTheDocument();
    expect(label).toBeInTheDocument();
    expect(error).toBeInTheDocument();
  });

  it("should evoke 'onChange' when textarea value changes", async () => {
    const { user, onChange } = renderHelper();

    const textarea = getByDisplayValue("vin123");
    await user.type(textarea, "4");
    expect(onChange).toHaveBeenNthCalledWith(1, "vin1234");
  });
});
