/*
 * Copyright (c) Jaguar Land Rover Ltd 2022. All rights reserved
 */

import { render, screen } from "@testing-library/react";
import { BrowserRouter } from "react-router-dom";
import userEvent from "@testing-library/user-event";
import { describe, expect, test, vi } from "vitest";
import Keycloak from "keycloak-js";
import { Navbar } from "components/App/Navbar";
import { Sidebar } from "components/App/Sidebar";

const kc = new Keycloak();

const renderHelper = (url = "http://localhost:3000") => {
  Object.defineProperty(window, "location", {
    value: new URL(url),
    writable: true
  });
  const sidebar = true;
  const sidebarFunc = vi.fn();
  render(
    <BrowserRouter>
      <Navbar showSidebar={sidebarFunc} sidebar={sidebar} />
      <Sidebar sidebar={sidebar} />
    </BrowserRouter>
  );
};

describe("Navbar Component", () => {
  const { getByRole, getByText, queryByText } = screen;

  test("All links load", () => {
    renderHelper();

    const guiName = getByText(/vcdp config center/i);
    const configLink = getByRole("link", { name: /config request/i });
    const controlLink = getByRole("link", { name: /control request/i });
    const dataProductLink = getByRole("link", { name: /data product request/i });
    const digitalVehicleLink = getByRole("link", { name: /digital vehicle/i });
    const requestHistoryLink = getByRole("link", { name: /request history/i });
    const environment = getByText("Development");

    expect(guiName).toBeInTheDocument();
    expect(configLink).toBeInTheDocument();
    expect(controlLink).toBeInTheDocument();
    expect(dataProductLink).toBeInTheDocument();
    expect(digitalVehicleLink).toBeInTheDocument();
    expect(requestHistoryLink).toBeInTheDocument();
    expect(environment).toBeInTheDocument();
  });

  test("Pre prod URL shows development environment", () => {
    renderHelper("https://vlc-config-center-gui.pre-prod.jlr-vcdp.com");

    const environment = getByText("Pre-Production");
    expect(environment).toBeInTheDocument();

    const devEnv = queryByText("Development");
    expect(devEnv).not.toBeInTheDocument();
  });

  test("prod URL shows development environment", () => {
    renderHelper("https://vlc-config-center-gui.prod.jlr-vcdp.com");

    const environment = getByText("Production");
    expect(environment).toBeInTheDocument();

    const devEnv = queryByText("Development");
    expect(devEnv).not.toBeInTheDocument();
  });

  test("logout button clicks", async () => {
    const user = userEvent.setup();
    renderHelper();

    const button = getByRole("button", { name: /Log Out/i });
    await user.click(button);
    expect(kc.logout).toBeCalled();
  });
});
