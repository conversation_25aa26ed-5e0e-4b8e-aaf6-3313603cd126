/*
 * Copyright (c) Jaguar Land Rover Ltd 2022. All rights reserved
 */

import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as Router } from "react-router-dom";
import { render, screen } from "@testing-library/react";
import { beforeEach, describe, expect, test } from "vitest";
import { Home } from "pages/Home";

describe("Home Page", () => {
  beforeEach(() => {
    render(
      <Router>
        <Home />
      </Router>
    );
  });

  test("should have a link to confluence page", () => {
    const confluenceLink = screen.getByRole("link", { name: /Config Center Guide/i });
    expect(confluenceLink).toBeInTheDocument();
    expect(confluenceLink).toHaveAttribute(
      "href",
      "https://confluence.devops.jlr-apps.com/display/VCDP/Config+Center+GUI"
    );
  });
});
