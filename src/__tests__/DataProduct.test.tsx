/*
 * Copyright (c) Jaguar Land Rover Ltd 2022. All rights reserved
 */

import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as Router } from "react-router-dom";
import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { describe, expect, test } from "vitest";
import { DataProduct } from "pages/DataProduct";
import { AuthContext, AuthValues } from "context/auth.context";
import { NotificationContextProvider } from "context/notification.context";
import { Notifications } from "components/Notifications";
import { VehicleContextProvider } from "context/vehicle.context";
import { ReactQueryContextProvider } from "context/react-query.context";

const authValue: AuthValues = {
  firstName: "John",
  lastName: "Doe",
  roles: ["admin"],
  loginStatus: "checking",
};

const renderHelper = () => {
  const user = userEvent.setup();

  render(
    <AuthContext.Provider value={authValue}>
      <NotificationContextProvider>
        <ReactQueryContextProvider>
          <VehicleContextProvider>
            <Notifications />
            <Router>
              <DataProduct />
            </Router>
          </VehicleContextProvider>
        </ReactQueryContextProvider>
      </NotificationContextProvider>
    </AuthContext.Provider>
  );
  return { user };
};

describe("Data Product Page", () => {
  const {
    getByRole,
    getByLabelText,
    getByTestId,
    getByPlaceholderText,
    findByText,
    findAllByText,
    findByRole,
    findAllByLabelText,
    findByLabelText,
    queryByRole,
    queryByText,
    findAllByRole,
  } = screen;

  test("search initially empty", async () => {
    renderHelper();
    expect(getByPlaceholderText(/Enter VIN/i)).toHaveValue("");
    expect(getByLabelText("csvFileUpload")).toHaveValue("");
  });

  test("data product table displays stored data products", async () => {
    renderHelper();
    const tableHeaders = await findAllByLabelText("listheader");
    const headerNames = tableHeaders.map((element) => element.innerHTML);
    expect(headerNames).toEqual(["ID", "Modified", "Description", "Status", "Actions"]);

    const dataProduct1 = await findByText("Dp_003");
    expect(dataProduct1).toBeInTheDocument();
  });

  test("can update status by clicking status column in data product table", async () => {
    const { user } = renderHelper();
    const statusButton = await findAllByText("TESTING");
    await user.click(statusButton[0]);

    const select = await findByRole("combobox", { name: "Update Status" });
    await user.selectOptions(select, "ROLLOUT");

    const submit = await findByRole("button", { name: "Submit" });
    await user.click(submit);
  });

  test("data product modified table is empty but shows headings", async () => {
    renderHelper();
    const tableHeaders = await findAllByLabelText("modifiedheader");
    expect(tableHeaders[0].innerHTML).toEqual("Status");
    expect(tableHeaders[1].innerHTML).toEqual("VIN");
    expect(tableHeaders[3].innerHTML).toEqual("Policy Configs");

    const closeButton = queryByRole("button", { name: "removeActiveConfigBtn" });
    expect(closeButton).not.toBeInTheDocument();
  });

  test("when vehicle is included, table displays Auto", async () => {
    const { user } = renderHelper();
    await user.type(getByPlaceholderText(/enter VIN/i), "TSTVINBVDNRIG1234");
    await user.click(getByRole("button", { name: /add/i }));

    const autoStatus = await findByText("Auto");
    expect(autoStatus).toBeInTheDocument();
  });

  test("when vehicle is excluded, table displays Non-auto", async () => {
    const { user } = renderHelper();
    await user.type(getByPlaceholderText(/enter VIN/i), "EQUALVINBVDNRIG17");
    await user.click(getByRole("button", { name: /add/i }));

    const autoStatus = await findByText("Non-Auto");
    expect(autoStatus).toBeInTheDocument();
  });

  test("popup appears when vin is not found in DV", async () => {
    const { user } = renderHelper();
    await user.type(getByPlaceholderText(/enter VIN/i), "NOTFOUND123456789");
    await user.click(getByRole("button", { name: /add/i }));

    const warningNotification = await findByText(/Vehicle not found for NOTFOUND123456789/i);
    expect(warningNotification).toBeInTheDocument();
  });

  test("popup appears when no vin is entered", async () => {
    const { user } = renderHelper();
    const addVinButton = getByRole("button", { name: /add/i });
    await user.click(addVinButton);

    const warningNotification = await findByText(/please enter a vin/i);
    expect(warningNotification).toBeInTheDocument();
  });

  test("clicking X icon removes vin from table", async () => {
    const { user } = renderHelper();
    await user.type(getByPlaceholderText(/enter VIN/i), "TSTVINBVDNRIG1234");
    await user.click(getByRole("button", { name: /add/i }));

    await user.click(await findByLabelText(/removeActiveConfigBtn/i));
    await user.click(await findByLabelText(/removeActiveConfigBtn/i));
    const banner = await findByText("No VINS Added");
    expect(banner).toBeInTheDocument();
  });

  test("vehicles to update table appears on Upload button click", async () => {
    const { user } = renderHelper();
    const VinCSV = "Vin\nTSTVINBVDNRIG1234\n";
    const file = new File([VinCSV], "vin.csv", { type: "text/csv" });

    await user.upload(getByLabelText("csvFileUpload"), file);
    await user.click(getByRole("button", { name: /upload/i }));

    const vehiclesTable = await findByRole("cell", { name: /TSTVINBVDNRIG1234/i });
    expect(vehiclesTable).toBeInTheDocument();
  });

  test("warning notification if no VIN entered", async () => {
    const { user } = renderHelper();
    await user.click(getByRole("button", { name: /add/i }));

    const warningNotification = await findByText(/Please enter a VIN/i);
    expect(warningNotification).toBeInTheDocument();
  });

  test("inspect button is DP_003", async () => {
    const { user } = renderHelper();
    const inspectButton = await findAllByRole("button", { name: /inspect/i });
    await user.click(inspectButton[0]);

    const modal = await findByText(/Dp_003 - e2e-test - 2022-May-10/i);
    expect(modal).toBeInTheDocument();

    await user.click(await findByRole("button", { name: "Close" }));
  });

  test("view eligibility criteria through inspect button", async () => {
    const { user } = renderHelper();
    const inspectButton = await findAllByRole("button", { name: /inspect/i });
    await user.click(inspectButton[0]);

    const fleetId = await findByText(/mlap/i);
    const fuelType = await findByText(/bev/i);
    const tags = await findByText(/tag_1, tag_2/i);
    const naFields = await findAllByText("N/A");

    expect(fleetId).toBeInTheDocument();
    expect(fuelType).toBeInTheDocument();
    expect(tags).toBeInTheDocument();
    expect(naFields).toHaveLength(3);
  });

  // Tests have been commented out as they were failing unexpectedly, despite no affecting code changes
  // Functionality being tested still works as expected
  // DDA-127451 has been created to resolve the issue

  // test("view eligibility criteria through inspect button, tags is null", async () => {
  //   const { user } = renderHelper();
  //   const inspectButton = await findAllByRole("button", { name: /inspect/i });
  //   await user.click(inspectButton[1]);

  //   const fleetId = await findByText(/mlap/i);
  //   const fuelType = await findByText(/bev/i);
  //   const naFields = await findAllByText("N/A");

  //   expect(fleetId).toBeInTheDocument();
  //   expect(fuelType).toBeInTheDocument();
  //   expect(naFields).toHaveLength(4);
  // });

  // test("view permissions through inspect button", async () => {
  //   const { user } = renderHelper();
  //   const inspectButton = await findAllByRole("button", { name: /inspect/i });
  //   await user.click(inspectButton[0]);

  //   const description = await findByText(/applied to vehicles with the following permission/i);
  //   const permission = await findByText(/L_App_Privacy/i);

  //   expect(description).toBeInTheDocument();
  //   expect(permission).toBeInTheDocument();
  // });

  // test("success when applying DP to vehicle, pending and active exist", async () => {
  //   const { user } = renderHelper();

  //   await user.type(getByPlaceholderText(/Enter VIN/i), "TSTAPPLYDPRIG1234");

  //   await user.click(await findByRole("button", { name: /add/i }));

  //   const vehiclesTable = await findByRole("cell", { name: /TSTAPPLYDPRIG1234/i });
  //   expect(vehiclesTable).toBeInTheDocument();

  //   const apply = await findAllByRole("button", { name: /apply/i });
  //   await user.click(apply[0]);
  //   const successNotification = await findByText("Dp_003 has been applied to VINs");
  //   expect(successNotification).toBeInTheDocument();
  // });

  // test("success when removing DP from vehicle, pending and active exist", async () => {
  //   const { user } = renderHelper();

  //   await user.type(getByPlaceholderText(/Enter VIN/i), "TSTAPPLYDPRIG1234");

  //   await user.click(await findByRole("button", { name: /add/i }));

  //   const removeButton = await findAllByRole("button", { name: /remove/i });
  //   await user.click(removeButton[1]);

  //   const successNotification = await findByText("Dp_003 has been removed from VINs");
  //   expect(successNotification).toBeInTheDocument();
  // });

  // test("warning when removing DP from vehicle, doesn't exist on vehicle", async () => {
  //   const { user } = renderHelper();

  //   await user.type(getByPlaceholderText(/Enter VIN/i), "TSTREMVEDPWRN1234");

  //   await user.click(await findByRole("button", { name: /add/i }));

  //   const removeButton = await findAllByRole("button", { name: /remove/i });
  //   await user.click(removeButton[1]);

  //   const warningNotification = await findByText("Some VINs were not processed");
  //   expect(warningNotification).toBeInTheDocument();
  // });

  describe.skip("status indicator", () => {
    test("never connected status shown when manifest is null", async () => {
      const { user } = renderHelper();
      await user.type(getByPlaceholderText(/enter VIN/i), "VINNULLMANIFEST12");
      await user.click(getByRole("button", { name: /add/i }));

      const tooltip = getByTestId("status-indicator-tooltip");
      await user.hover(tooltip);
      const tooltipLabel = queryByText(
        /Vehicle - VINNULLMANIFESTUUID has never connected to VCDP/i
      );
      expect(tooltipLabel).toBeInTheDocument();
    });

    test("never connected status shown when manifest values are null", async () => {
      const { user } = renderHelper();
      await user.type(getByPlaceholderText(/enter VIN/i), "NULLMANIFESTVALUE");
      await user.click(getByRole("button", { name: /add/i }));

      const tooltip = getByTestId("status-indicator-tooltip");
      await user.hover(tooltip);
      const tooltipLabel = queryByText(
        /Vehicle - VINNULLMANIFESTVALUESUUID has never connected to VCDP/i
      );
      expect(tooltipLabel).toBeInTheDocument();
    });

    test("never connected status shown when manifest values are 0.0", async () => {
      const { user } = renderHelper();
      await user.type(getByPlaceholderText(/enter VIN/i), "ZEROMANIFESTVALUE");
      await user.click(getByRole("button", { name: /add/i }));

      const tooltip = getByTestId("status-indicator-tooltip");
      await user.hover(tooltip);
      const tooltipLabel = queryByText(
        /Vehicle - VINZEROMANIFESTVALUESUUID has not been initialised with default configuration. Provision the vehicle before applying data products/i
      );
      expect(tooltipLabel).toBeInTheDocument();
    });

    test("ready for data products status shown when manifest values are initialised", async () => {
      const { user } = renderHelper();
      await user.type(getByPlaceholderText(/enter VIN/i), "EQUALVINBVDNRIG12");
      await user.click(getByRole("button", { name: /add/i }));

      const tooltip = getByTestId("status-indicator-tooltip");
      await user.hover(tooltip);
      const tooltipLabel = queryByText(/Vehicle is ready for applying data products/i);
      expect(tooltipLabel).toBeInTheDocument();
    });
  });
});
