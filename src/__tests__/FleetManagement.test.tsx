/*
 * Copyright 2023 (c) Jaguar Land Rover Ltd. All rights reserved.
 */

import { describe, expect, it } from "vitest";
import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { NotificationContextProvider } from "context/notification.context";
import { Notifications } from "components/Notifications";
import { FleetContextProvider } from "context/fleet.context";
import { ReactQueryContextProvider } from "../context/react-query.context";
import { FleetManagement } from "../pages/FleetManagement";

const vinToAdd = "EQUALVINBVDNRIG17";
const vinToRemove = "TSTVINBVDNRIG1234";

const renderHelper = () => {
  const user = userEvent.setup();
  render(
    <ReactQueryContextProvider>
      <NotificationContextProvider>
        <FleetContextProvider>
          <FleetManagement />
        </FleetContextProvider>
        <Notifications />
      </NotificationContextProvider>
    </ReactQueryContextProvider>
  );
  return { user };
};

describe("FleetManagement Component", () => {
  const { findByText, getByLabelText, getByRole, findByRole, findByLabelText } = screen;

  it("should display fleet count on load", async () => {
    renderHelper();
    const vehicleCount = await findByText("3 Vehicles");

    expect(vehicleCount).toBeInTheDocument();
  });

  it("should display no vins screen on load", async () => {
    renderHelper();
    const noVinsDescription = await findByText("Please enter some VINs to update MLAP Fleet");

    expect(noVinsDescription).toBeInTheDocument();
  });

  it("should upload csv and add/remove fleet ids on SUBMIT", async () => {
    //upload a csv with two VINs
    const { user } = renderHelper();
    const VinCSV = "FLEETVINBVDNRIG12\n";
    const file = new File([VinCSV], "vin.csv", { type: "text/csv" });
    await user.upload(getByLabelText("csvFileUpload"), file);
    await user.click(getByRole("button", { name: /upload/i }));

    const removedMessage = await findByText("1 VIN will be removed");
    const addedMessage = await findByText("3 VINs will be added");
    expect(removedMessage).toBeInTheDocument();
    expect(addedMessage).toBeInTheDocument();

    //on submit, 200 is returned
    await user.click(getByRole("button", { name: /submit/i }));
    const successMessage = await findByText(
      "MLAP fleet has been updated with the provided vehicles"
    );
    expect(successMessage).toBeInTheDocument();
  });

  it("should show invalid VINs when csv contains invalid vins", async () => {
    //upload a csv with invalid VIN
    const { user } = renderHelper();
    const VinCSV = "FLEETVINBVDNRIG12\nVin\nerrorVin\n";
    const file = new File([VinCSV], "vin.csv", { type: "text/csv" });
    await user.upload(getByLabelText("csvFileUpload"), file);
    await user.click(getByRole("button", { name: /upload/i }));

    const invalidVinInfo = await findByText("3 Invalid VINs");
    expect(invalidVinInfo).toBeInTheDocument();

    //test popover with invalid vins dropdown
    const viewVins = await findByLabelText("warning-label");
    await user.click(viewVins);
    const invalidVin = await findByText("errorVin");
    expect(invalidVin).toBeInTheDocument();
  });

  it("should reset when CANCEL is clicked", async () => {
    //upload a csv with one VIN
    const { user } = renderHelper();
    const VinCSV = "FLEETVINBVDNRIG12\n";
    const file = new File([VinCSV], "vin.csv", { type: "text/csv" });
    await user.upload(getByLabelText("csvFileUpload"), file);
    await user.click(getByRole("button", { name: /upload/i }));

    //on cancel, flow is reset
    const cancelButton = await findByRole("button", { name: /cancel/i });
    await user.click(cancelButton);
    const resetMessage = await findByText("Please enter some VINs to update MLAP Fleet");
    expect(resetMessage).toBeInTheDocument();
  });

  it("should show vins to update when warning label is clicked", async () => {
    //upload a csv with one VIN
    const { user } = renderHelper();
    const VinCSV = "FLEETVINBVDNRIG12\n";
    const file = new File([VinCSV], "vin.csv", { type: "text/csv" });
    await user.upload(getByLabelText("csvFileUpload"), file);
    await user.click(getByRole("button", { name: /upload/i }));

    const warningIcon = await findByLabelText("warning-label");
    await user.click(warningIcon);
    const vinSearch = await findByText("FLEETVINBVDNRIG12");
    expect(vinSearch).toBeInTheDocument();
  });

  it("should show notification when csv and dv vins are the same", async () => {
    //upload a csv with two VINs
    const { user } = renderHelper();
    const VinCSV = "EQUALVINBVDNRIG12\n";
    const file = new File([VinCSV], "vin.csv", { type: "text/csv" });
    await user.upload(getByLabelText("csvFileUpload"), file);
    await user.click(getByRole("button", { name: /upload/i }));

    const warningNotification = await findByText(
      "Vehicles fleet ID already up to date with CSV uploaded"
    );
    expect(warningNotification).toBeInTheDocument();
  });

  it("should add a single vin on add button click", async () => {
    //upload a csv with two VINs
    const { user } = renderHelper();
    const configName = getByRole("searchbox");
    await user.type(configName, vinToAdd);
    await user.click(getByRole("button", { name: /add/i }));

    const changesToBeMade = await findByText("1 VIN will be added");
    expect(changesToBeMade).toBeInTheDocument();
  });

  it("should show notification if single vin is not present in MLAP fleet", async () => {
    //upload a csv with two VINs
    const { user } = renderHelper();
    const configName = getByRole("searchbox");
    await user.type(configName, vinToAdd);
    await user.click(getByRole("button", { name: /remove/i }));

    const changesToBeMade = await findByText("Vehicle does not exist in MLAP fleet");
    expect(changesToBeMade).toBeInTheDocument();
  });

  it("should remove a single vin on remove button click", async () => {
    //upload a csv with two VINs
    const { user } = renderHelper();
    const configName = getByRole("searchbox");
    await user.type(configName, vinToRemove);
    await user.click(getByRole("button", { name: /remove/i }));

    const changesToBeMade = await findByText("1 VIN will be removed");
    expect(changesToBeMade).toBeInTheDocument();
  });

  it("should show notification if single vin added exists already", async () => {
    //upload a csv with two VINs
    const { user } = renderHelper();
    const configName = getByRole("searchbox");
    await user.type(configName, vinToRemove);
    await user.click(getByRole("button", { name: /add/i }));

    const changesToBeMade = await findByText("Vehicle already present in MLAP fleet");
    expect(changesToBeMade).toBeInTheDocument();
  });
});
