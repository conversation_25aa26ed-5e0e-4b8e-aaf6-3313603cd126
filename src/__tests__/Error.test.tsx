/*
 * Copyright (c) Jaguar Land Rover Ltd 2022. All rights reserved
 */

import { MemoryRouter } from "react-router-dom";
import { render, screen } from "@testing-library/react";
import { describe, expect, test } from "vitest";
import userEvent from "@testing-library/user-event";
import { ErrorFallback, InternalServerError, NotFoundError } from "pages/Errors";
import { ErrorPage } from "components/Errors";

const renderHelper = (route = "/", errorType = "default", message = "AN ERROR HAS OCCURRED.") => {
  const user = userEvent.setup();

  render(
    <MemoryRouter initialEntries={[route]}>
      {errorType === "default" && <ErrorPage />}
      {errorType === "fallback" && <ErrorFallback error={{ message: message }} />}
      {errorType === "server" && <InternalServerError />}
      {errorType === "notFound" && <NotFoundError />}
    </MemoryRouter>
  );

  return { user };
};

describe("Error Page", () => {
  const { getByRole, getByText } = screen;

  test("should have two anchor links and a generic message", () => {
    renderHelper();

    const homeButton = getByRole("button", { name: /return to home page/i });
    expect(homeButton).toBeInTheDocument();

    const refreshButton = getByRole("button", { name: /refresh page/i });
    expect(refreshButton).toBeInTheDocument();

    const message = getByText(/message: an error has occurred/i);
    expect(message).toBeInTheDocument();
  });

  test("internal server error message", () => {
    renderHelper("/", "server");

    const message = getByText(/message: internal server error/i);
    expect(message).toBeInTheDocument();
  });

  test("not found error message", () => {
    renderHelper("/", "notFound");

    const message = getByText(/message: page not found/i);
    expect(message).toBeInTheDocument();
  });

  test("should have an error message", () => {
    renderHelper("/", "fallback", "error boundary test");
    const message = getByText(/message: error boundary test/i);
    expect(message).toBeInTheDocument();
  });
});
