/*
 * Copyright 2023 (c) Jaguar Land Rover Ltd. All rights reserved.
 */

import { describe, expect, it } from "vitest";
import { ReactNode } from "react";
import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { Tooltip } from "../components/Overlays";

const renderHelper = (label: string | ReactNode) => {
  const user = userEvent.setup();
  render(
    <Tooltip label={label}>
      <div>Target</div>
    </Tooltip>
  );
  return { user };
};

describe("Tooltip Component", () => {
  const { getByText, queryByText, findByText } = screen;

  it("should display correct tooltip if label is string", async () => {
    const { user } = renderHelper("String Tooltip");
    await user.hover(getByText("Target"));
    expect(await findByText("String Tooltip")).toBeInTheDocument();
  });

  it("should display correct tooltip if label is JSX", async () => {
    const { user } = renderHelper(<h1>Tooltop Header</h1>);
    await user.hover(getByText("Target"));
    expect(await findByText("Tooltop Header")).toBeInTheDocument();
  });

  it("should hide tooltip when unhovering target", async () => {
    const { user } = renderHelper("Tooltip");
    const target = getByText("Target");
    await user.hover(target);
    user.unhover(target);
    await waitFor(() => {
      expect(queryByText("Tooltip")).not.toBeInTheDocument();
    });
  });

  it("should hide tooltip when Escape key is pressed", async () => {
    const { user } = renderHelper("Tooltip");
    await user.hover(getByText("Target"));
    user.keyboard("[Escape]");
    await waitFor(() => {
      expect(queryByText("Tooltip")).not.toBeInTheDocument();
    });
  });
});
