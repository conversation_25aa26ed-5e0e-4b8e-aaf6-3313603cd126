/*
 * Copyright 2023 (c) Jaguar Land Rover Ltd. All rights reserved.
 */

import { render, screen } from "@testing-library/react";
import { beforeEach, describe, expect, it, vi } from "vitest";
import userEvent from "@testing-library/user-event";
import { Select } from "components/Form";

describe("Select Component", () => {
  const onChange = vi.fn();
  const { getByRole, queryByLabelText, queryByText } = screen;

  const selectOptions = [
    { label: "All", value: "all" },
    { label: "Active", value: "true" },
    { label: "Deactive", value: "false" },
  ];

  beforeEach(() => {
    render(<Select label="Select" onChange={onChange} options={selectOptions} value={"All"} />);
  });

  it("should display select box, label and initial option", () => {
    const select = queryByLabelText("Select");
    const option1 = queryByText("All");

    expect(select).toBeInTheDocument();
    expect(option1).toBeInTheDocument();
  });

  it("should call onChange when an option is clicked", async () => {
    const user = userEvent.setup();

    const select = getByRole("combobox", { name: /select/i });
    await user.selectOptions(select, "Deactive");
    expect(onChange).toHaveBeenCalledWith("false");
  });
});
