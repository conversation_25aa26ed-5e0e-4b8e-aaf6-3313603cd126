/*
 * Copyright (c) Jaguar Land Rover Ltd 2022. All rights reserved
 */

import { <PERSON>rowser<PERSON>outer as Router } from "react-router-dom";
import { act, render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { describe, expect, test } from "vitest";
import { Config } from "pages/Config";
import { AuthContext, AuthValues } from "context/auth.context";
import { NotificationContextProvider } from "context/notification.context";
import { Notifications } from "components/Notifications";
import { ReactQueryContextProvider } from "context/react-query.context";

const authValue: AuthValues = {
  firstName: "John",
  lastName: "Doe",
  roles: ["admin"],
  loginStatus: "checking",
};

const renderHelper = () => {
  const user = userEvent.setup();

  act(() => {
    render(
      <AuthContext.Provider value={authValue}>
        <NotificationContextProvider>
          <ReactQueryContextProvider>
            <Router>
              <Config />
            </Router>
          </ReactQueryContextProvider>
          <Notifications />
        </NotificationContextProvider>
      </AuthContext.Provider>
    );
  });

  return { user };
};

describe("Config Page", () => {
  const { getByRole, getByPlaceholderText, findByText, queryByText } = screen;

  describe("Manifest and Feedback section", () => {
    test("Notification appears when no vin entered for get status", async () => {
      const { user } = renderHelper();
      const statusButton = getByRole("button", { name: /search/i });
      await user.click(statusButton);

      const infoNotification = await findByText(/please enter a vin/i);
      expect(infoNotification).toBeInTheDocument();
    });

    test("Invalid VIN shows notification error", async () => {
      const { user } = renderHelper();
      const inputVin = getByPlaceholderText(/enter vin/i);
      await user.type(inputVin, "otherError1234567");

      const statusButton = getByRole("button", { name: /search/i });
      await user.click(statusButton);

      const errorNotification = await findByText(/something went wrong/i);
      expect(errorNotification).toBeInTheDocument();
    });

    test("vehicle status NE shows all good message", async () => {
      const { user } = renderHelper();
      const inputVin = getByPlaceholderText(/enter vin/i);
      await userEvent.type(inputVin, "allGood");

      const statusButton = getByRole("button", { name: /search/i });
      await user.click(statusButton);

      const prevErrorsBtn = queryByText(/No errors on this vehicle./i);
      expect(prevErrorsBtn).toBeInTheDocument();
    });
  });

  describe("Create Config section", () => {
    test("create config form updates on input", async () => {
      const { user } = renderHelper();
      const configType = getByRole("combobox", { name: /default select example/i });
      await user.selectOptions(configType, "Ruleset");

      const ruleset = getByRole("option", { name: "Ruleset" }) as HTMLOptionElement;
      expect(ruleset.selected).toBe(true);

      const configName = getByRole("textbox", { name: /name */i });
      await user.type(configName, "config1");
      expect(configName).toHaveValue("config1");

      const configDescription = getByRole("textbox", { name: /description */i });
      await user.type(configDescription, "description");
      expect(configDescription).toHaveValue("description");

      const configFile = getByRole("textbox", { name: /file */i });
      await user.type(configFile, JSON.stringify("{{'textBox': 1}"));
      expect(configFile).toHaveValue(JSON.stringify("{'textBox': 1}"));

      const createButton = getByRole("button", { name: /create configuration/i });
      expect(createButton).toBeInTheDocument();

      await user.click(createButton);
      const successNotification = await findByText(/Configuration Created/i);
      expect(successNotification).toBeInTheDocument();
    });

    test("missing some info from config form gives notification message", async () => {
      const { user } = renderHelper();
      const configType = getByRole("combobox", { name: /default select example/i });
      await user.selectOptions(configType, "Query");

      const configName = getByRole("textbox", { name: /name */i });
      await user.type(configName, "config1");

      const configFile = getByRole("textbox", { name: /file */i });
      await user.type(configFile, "{}".replace(/[{[]/g, "$&$&"));

      const createButton = getByRole("button", { name: /create configuration/i });
      await user.click(createButton);

      const warningNotification = await findByText(/All required fields have not been entered/i);
      expect(warningNotification).toBeInTheDocument();
    });

    test("configuration click with signal dictionary choice", async () => {
      const { user } = renderHelper();
      const configType = getByRole("combobox", { name: /default select example/i });
      await user.selectOptions(configType, "Signal Dictionary");

      const configName = getByRole("textbox", { name: /name */i });
      await user.type(configName, "config2");

      const configDescription = getByRole("textbox", { name: /description */i });
      await user.type(configDescription, "description2");

      const configFile = getByRole("textbox", { name: /file */i });
      await user.type(configFile, JSON.stringify("{{'textBox': 1}"));

      const createButton = getByRole("button", { name: /create configuration/i });
      await user.click(createButton);

      const successNotification = await findByText(/Configuration Created/i);
      expect(successNotification).toBeInTheDocument();
    });

    test("Error with Configuration when Channel selected", async () => {
      const { user } = renderHelper();
      const configType = getByRole("combobox", { name: /default select example/i });
      await user.selectOptions(configType, "Channel");

      const configName = getByRole("textbox", { name: /name */i });
      await user.type(configName, "config3");

      const configDescription = getByRole("textbox", { name: /description */i });
      await user.type(configDescription, "description3");

      const configFile = getByRole("textbox", { name: /file */i });
      await user.type(configFile, JSON.stringify("{{'textBox': 1}"));

      const createButton = getByRole("button", { name: /create configuration/i });
      await user.click(createButton);

      const errorNotification = await findByText(/Forbidden/i);
      expect(errorNotification).toBeInTheDocument();
    });
  });
});
