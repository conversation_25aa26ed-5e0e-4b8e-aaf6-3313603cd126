/*
 * Copyright 2023 (c) Jaguar Land Rover Ltd. All rights reserved.
 */

import { render, screen } from "@testing-library/react";
import { describe, expect, it, vi } from "vitest";
import userEvent from "@testing-library/user-event";
import { TextInput } from "components/Form";

const renderHelper = (label = "", error = "") => {
  const user = userEvent.setup();
  const onChange = vi.fn();
  render(
    <TextInput
      description="Description"
      error={error}
      label={label}
      onChange={onChange}
      value="vin123"
    />
  );
  return { user, onChange };
};

describe("TextInput Component", () => {
  const { queryByDisplayValue, queryByText, getByDisplayValue } = screen;

  it("it should display correct value, label, description and error message", () => {
    renderHelper("VIN", "incorrect value");

    const value = queryByDisplayValue("vin123");
    const label = queryByText("VIN");
    const desc = queryByText("Description");
    const error = queryByText("incorrect value");

    expect(value).toBeInTheDocument();
    expect(label).toBeInTheDocument();
    expect(desc).toBeInTheDocument();
    expect(error).toBeInTheDocument();
  });

  it("should evoke 'onChange' when input value changes", async () => {
    const { user, onChange } = renderHelper();

    const input = getByDisplayValue("vin123");
    await user.type(input, "4");
    expect(onChange).toHaveBeenNthCalledWith(1, "vin1234");
  });
});
