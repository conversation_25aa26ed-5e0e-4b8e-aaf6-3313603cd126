/*
 * Copyright 2023 (c) Jaguar Land Rover Ltd. All rights reserved.
 */

import { Paths } from "types/paths";

interface Validation {
  required?: boolean;
  min?: number;
  max?: number;
}

export type Validations<T> = Partial<Record<Paths<T>, Validation>>;

export const validateField = (value: unknown, validation: Validation) => {
  const { required, min, max } = validation;

  if (required) {
    if (
      value === undefined ||
      (typeof value === "string" && value == "") ||
      (Array.isArray(value) && !value.length)
    ) {
      return "This field is required";
    }
  }

  if (min && typeof value === "string" && value.length < min && value.length > 0)
    return `Must be at least ${min} characters`;

  if (max && typeof value === "string" && value.length > max)
    return `Must not be larger that ${max} characters`;

  return "";
};
