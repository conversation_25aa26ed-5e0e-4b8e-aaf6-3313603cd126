/* eslint-disable id-length */
/*
 * Copyright 2023 (c) Jaguar Land Rover Ltd. All rights reserved.
 */

import { FormEvent, useCallback, useState } from "react";
import getPropValue from "lodash/get";
import setPropValue from "lodash/set";
import { Paths } from "types/paths";
import { validateField, Validations } from "./";

type Errors<T> = Partial<Record<Paths<T>, string>>;
type ValueOf<T> = T[keyof T];

interface UseFormProps<T> {
  defaultValues: T;
  validations?: Validations<T>;
}

export const useForm = <T extends object>({ defaultValues, validations }: UseFormProps<T>) => {
  const [formValues, setFormValues] = useState<T>(() => defaultValues);
  const [errors, setErrors] = useState<Errors<T>>(() => ({}));

  /**
   * Returns a callback function that's executed when
   * the field with the given key is changed.
   */
  const onChange = useCallback(
    (key: Paths<T>) => {
      return (newValue: ValueOf<T>) => {
        const validation = validations?.[key];
        if (validation) {
          const validationResult = validateField(newValue, validation);
          setErrors((oldErrors) => ({
            ...oldErrors,
            ...{ [key]: validationResult },
          }));
        }
        setFormValues((oldFormValues) => ({
          ...setPropValue<T>(oldFormValues, key, newValue),
        }));
      };
    },
    [validations]
  );

  /**
   * Checks if form is valid.
   * If invalid, populates the 'errors' object with error messages.
   */
  const validate = useCallback(() => {
    let checkIfValid = true;
    if (validations) {
      for (const key in validations) {
        const value = getPropValue(formValues, key);
        const validation = validations?.[key as Paths<T>];
        if (!validation) continue;
        const validationResult = validateField(value, validation);
        if (validationResult) {
          checkIfValid = false;
          setErrors((oldErrors) => ({
            ...oldErrors,
            ...{ [key]: validationResult },
          }));
        }
      }
    }
    return checkIfValid;
  }, [validations, formValues]);

  /**
   * Checks if form is valid.
   * If valid, calls a callback function that was passed into it.
   */
  const onSubmit = useCallback(
    (callback: (_formValues: T) => void) => {
      return (e: FormEvent) => {
        e.preventDefault();
        if (validate()) callback(formValues);
      };
    },
    [formValues, validate]
  );

  return { onChange, onSubmit, formValues, errors };
};
