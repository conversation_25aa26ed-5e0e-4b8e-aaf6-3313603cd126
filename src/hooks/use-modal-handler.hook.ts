/*
 * Copyright (c) Jaguar Land Rover Ltd 2022. All rights reserved
 */

import { Modal } from "bootstrap";
import { useEffect, useState } from "react";

/*
  What?
  This hook create a reference of the modal using the id parameter passed.
  For example: DataProductVersions.js uses "activeConfigModal".

  Why?
  This hook allows the user to control showing and hiding the modal via
  Javascript instead of data attributes. This way, when a button is clicked,
  certain checks can be made before the modal is shown.

  More here: https://getbootstrap.com/docs/5.0/components/modal/#via-javascript
*/
export const useModalHandler = (id: string) => {
  const [useModal, setUseModal] = useState<Modal>();

  useEffect(() => {
    const element = document.getElementById(id);
    if (element) setUseModal(new Modal(element));
  }, [id]);

  return { useModal };
};
