/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

import { useCallback, useMemo, useState } from "react";

export const useSearchFilter = <T>(
  arrayData: T[],
  filterField: "comment" | "name" | "application"
) => {
  const [searchTerm, setSearchTerm] = useState("");

  const filteredItems = useMemo(() => {
    if (!searchTerm) return arrayData;
    return arrayData.filter((item: T) => {
      const val = item[filterField as keyof T] as string;
      return val.toLowerCase().includes(searchTerm.toLowerCase());
    });
  }, [arrayData, filterField, searchTerm]);

  const searchFilter = useCallback(({ target }: { target: HTMLInputElement }) => {
    setSearchTerm(target.value);
  }, []);

  return { searchFilter, filteredItems };
};
