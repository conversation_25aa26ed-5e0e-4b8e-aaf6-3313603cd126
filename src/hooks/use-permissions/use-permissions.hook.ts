/*
 * Copyright 2023 (c) Jaguar Land Rover Ltd. All rights reserved.
 */

import { useCallback, useContext } from "react";
import { AuthContext } from "context/auth.context";
import { Feature, Role } from "types/permissions";
import { extractEnvFromHostname } from "utils/constants";
import { permissions } from "./permissions";

const hostname = window.location.hostname;
const env = extractEnvFromHostname(hostname);

export const usePermissions = () => {
  const { roles } = useContext(AuthContext);

  const hasPermission = useCallback(
    (feature: Feature) => {
      return permissions[feature][env].some((role: Role) => roles.includes(role));
    },
    [roles]
  );

  return { hasPermission };
};
