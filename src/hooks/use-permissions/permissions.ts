/*
 * Copyright 2023 (c) Jaguar Land Rover Ltd. All rights reserved.
 */

import { Enviroment, Feature, Role } from "types/permissions";

type Permissions = Record<Feature, Record<Enviroment, Role[]>>;

export const permissions: Permissions = {
  Config: {
    dev: ["tester", "admin"],
    "pre-prod": ["tester", "admin"],
    preprod: ["tester", "admin"],
    prod: [],
  },
  Control: {
    dev: ["tester", "admin", "standard"],
    "pre-prod": ["tester", "admin", "standard"],
    preprod: ["tester", "admin", "standard"],
    prod: ["admin", "standard"],
  },
  DataProduct: {
    dev: ["tester", "admin", "standard"],
    "pre-prod": ["tester", "admin", "standard"],
    preprod: ["tester", "admin", "standard"],
    prod: ["admin", "standard"],
  },
  DigitalVehicle: {
    dev: ["tester", "admin", "standard"],
    "pre-prod": ["tester", "admin", "standard"],
    preprod: ["tester", "admin", "standard"],
    prod: ["admin", "standard"],
  },
  RequestHistory: {
    dev: ["tester", "admin", "standard"],
    "pre-prod": ["tester", "admin", "standard"],
    preprod: ["tester", "admin", "standard"],
    prod: ["admin", "standard"],
  },
  FleetManagement: {
    dev: ["tester", "admin", "fleet-id-manager"],
    "pre-prod": ["tester", "admin", "fleet-id-manager"],
    preprod: ["tester", "admin", "fleet-id-manager"],
    prod: ["admin", "fleet-id-manager"],
  },
  Obg: {
    //Roles to be expanded once page is fully complete
    dev: ["admin"],
    "pre-prod": [],
    preprod: [],
    prod: [],
  },
  ApplyControls: {
    dev: ["tester", "admin"],
    "pre-prod": ["tester", "admin"],
    preprod: ["tester", "admin"],
    prod: ["admin"],
  },
  VehicleTags: {
    dev: ["tester", "admin"],
    "pre-prod": ["tester", "admin"],
    preprod: ["tester", "admin"],
    prod: ["admin"],
  },
  VehicleProfile: {
    dev: ["tester", "admin"],
    "pre-prod": ["tester", "admin"],
    preprod: ["tester", "admin"],
    prod: [],
  },
};
