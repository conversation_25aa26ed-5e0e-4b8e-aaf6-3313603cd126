/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

import { useState } from "react";

export const usePagination = <T>(arrayData: T[]) => {
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const total = arrayData.length;

  const handlePageChange = (pageNum: number) => {
    setPage(page + pageNum);
  };

  const data = arrayData.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);
  return { data, handlePageChange, page, rowsPerPage, setPage, setRowsPerPage, total };
};
