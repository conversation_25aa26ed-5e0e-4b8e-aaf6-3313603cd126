/*
 * Copyright 2022 (c) Jaguar Land Rover Ltd. All rights reserved.
 */

import { useEffect, useRef } from "react";

const events = ["keypress", "mousemove", "touchmove", "click", "scroll"];

export const useIdle = (timeout: number, callback: () => void) => {
  const timer = useRef<number>();

  useEffect(() => {
    const handleEvents = () => {
      if (timer.current) window.clearTimeout(timer.current);
      timer.current = window.setTimeout(callback, timeout);
    };

    handleEvents();

    events.forEach((evt) => document.addEventListener(evt, handleEvents));

    return () => {
      events.forEach((evt) => document.removeEventListener(evt, handleEvents));
    };
  }, [timeout, callback]);
};
