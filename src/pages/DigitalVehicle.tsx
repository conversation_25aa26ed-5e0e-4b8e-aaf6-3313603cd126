/*
 * Copyright (c) Jaguar Land Rover Ltd 2022. All rights reserved
 */

import { FormEvent, useEffect, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { useDocTitle } from "hooks/use-doc-title.hook";
import { SingleInput } from "components/VinInput";
import {
  CreateRig,
  DvDataDisplay,
  ObgChange,
  VehicleProfile,
  VehicleTagChange,
} from "components/DigitalVehicle";
import { DEFAULT_VEHICLE_DATA, Vehicle } from "types/data";
import { useNotification } from "hooks/use-notification.hook";
import { getVehicles } from "api/digital-vehicle.api";
import { Card } from "components/Card";
import { Spinner } from "components/Spinner";
import { Accordian } from "components/Accordian";
import { usePermissions } from "hooks/use-permissions";
import { TabsContextProvider } from "context/tabs.context";
import { Tabs } from "components/Tabs/Tabs";
import { getVehicleTags } from "../api/data-product-manager.api";

export function DigitalVehicle() {
  const [vehicle, setVehicle] = useState<Vehicle>();
  const [vin, setVin] = useState("");
  const [rigVin, setRigVin] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [shouldPoll, setShouldPoll] = useState(false);
  const [createRigLoading, setCreateRigLoading] = useState(false);
  // const queryClient = useQueryClient();

  const { notify } = useNotification();
  const { hasPermission } = usePermissions();

  useDocTitle("Digital Vehicle");

  const { refetch: queryVehicles } = useQuery({
    queryKey: ["vin", vin],
    queryFn: () => getVehicles({ vins: [vin] }),
    enabled: false,
  });

  const { data: rigData = DEFAULT_VEHICLE_DATA } = useQuery({
    queryKey: ["rig"],
    queryFn: () => getVehicles({ vins: [rigVin] }),
    // refetchInterval(query) {
    //   console.log(query.state.dataUpdateCount);
    //   if (shouldPoll && query.state.dataUpdateCount < 5) {
    //     return 2000;
    //   }
    //   setShouldPoll(false);
    //   queryClient.removeQueries({ queryKey: ["rig"] });
    //   return false;
    // },
    refetchInterval: shouldPoll ? 2000 : false,
    enabled: shouldPoll,
  });

  const { data: distinctTags, isError: distinctTagsError } = useQuery({
    queryKey: ["get-all-distinct-tags"],
    queryFn: () => getVehicleTags(),
    enabled: !!vehicle,
  });

  useEffect(() => {
    if (distinctTagsError) {
      notify({
        type: "error",
        title: "Error pulling back distinctTags",
      });
    }
  }, [distinctTagsError, notify]);

  useEffect(() => {
    if (rigData.data.data.length > 0) {
      setShouldPoll(false);
    }
  }, [rigData]);

  const searchVehicle = async (ev?: FormEvent) => {
    if (ev) {
      ev.preventDefault();
    }

    if (!vin) {
      notify({
        type: "warning",
        title: "Please enter a VIN",
      });
    }
    setIsLoading(true);
    const { data: vehicles } = await queryVehicles();
    if (vehicles?.status === 200) {
      setVehicle(vehicles.data.data[0]);
    } else {
      notify({
        type: "error",
        title: vehicles?.data.errors ? vehicles.data.errors[0].message : "Error getting vehicle",
      });
    }
    setIsLoading(false);
  };

  return (
    <div className="container-fluid">
      <div className="p-2">
        <TabsContextProvider>
          <Tabs
            data={[
              {
                key: "search",
                tab: "Vehicle Search",
                panel: (
                  <div className="row my-4">
                    <div className="col-lg-3 col-md-12">
                      <Card title="Search Digital Vehicle">
                        {isLoading ? (
                          <Spinner />
                        ) : (
                          <SingleInput onVinEntered={searchVehicle} setVinEntered={setVin} />
                        )}
                      </Card>
                      {vehicle && (
                        <div className="pt-4">
                          <Accordian
                            items={[
                              {
                                heading: "Update Vehicle Type",
                                content: (
                                  <ObgChange
                                    obg={vehicle?.applications?.obg}
                                    vehicleArchitecture={vehicle?.assets?.vehicleArchitecture}
                                    searchVehicle={searchVehicle}
                                    uniqueId={vehicle?.identity?.uniqueId}
                                  />
                                ),
                              },
                              {
                                heading: "Update Tags",
                                content: (
                                  <VehicleTagChange
                                    distinctTagList={distinctTags?.data || []}
                                    originalActiveTags={vehicle?.tags}
                                    searchVehicle={searchVehicle}
                                    vin={vehicle?.identity.vin}
                                  />
                                ),
                              },
                              {
                                heading: "Add Vehicle Profile",
                                content: (
                                  <VehicleProfile
                                    searchVehicle={searchVehicle}
                                    vin={vehicle.identity.vin}
                                  />
                                ),
                                permission: hasPermission("VehicleProfile"),
                              },
                            ]}
                          />
                        </div>
                      )}
                    </div>
                    <div className="col-lg-1 d-none d-lg-block" />
                    <div className="col-lg-8 col-md-12">
                      <DvDataDisplay dvData={vehicle} searchVehicle={searchVehicle} />
                    </div>
                  </div>
                ),
              },
              {
                key: "rig",
                tab: "Create Rig",
                panel: (
                  <div className="row my-4">
                    <div className="col-lg-3 col-md-12">
                      <CreateRig
                        createRigLoading={createRigLoading}
                        setCreateRigLoading={setCreateRigLoading}
                        setRigVin={setRigVin}
                        setShouldPoll={setShouldPoll}
                        shouldPoll={shouldPoll}
                      />
                    </div>
                    <div className="col-lg-1 d-none d-lg-block" />
                    <div className="col-lg-8 col-md-12 position-relative">
                      {(createRigLoading || shouldPoll) && (
                        <div className="loadingWindow">
                          <div>
                            <Spinner />
                            <p className="m-0">
                              <em>
                                <strong>Creating Rig...</strong>
                              </em>
                            </p>
                            <p className="m-0">
                              <em>
                                <strong>May take some time</strong>
                              </em>
                            </p>
                          </div>
                        </div>
                      )}
                      <DvDataDisplay dvData={rigData.data.data[0]} searchVehicle={searchVehicle} />
                    </div>
                  </div>
                ),
              },
            ]}
          />
        </TabsContextProvider>
      </div>
    </div>
  );
}
