/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

import { useEffect, useState } from "react";
import { ENV_CONTEXT } from "utils/constants";
import { Select, TextInput } from "components/Form";
import { Card } from "components/Card";
import { Spinner } from "components/Spinner";
import { useForm } from "hooks/use-form";
import { useNotification } from "hooks/use-notification.hook";
import { IdentityI, VehicleResponse } from "types/forgerock";
import { TableItem } from "components/FRIdentity/DisplayContent";
import { get } from "services/fetch.service";
import { randomID } from "utils/random";

const validations = {
  inputValue: { required: true, min: 2, max: 100 },
  identifierValue: { required: true },
};

const options = [
  {
    label: "VIN",
    value: "vin",
  },
  {
    label: "Client ID",
    value: "client-id",
  },
  {
    label: "Vehicle ID",
    value: "vehicle-id",
  },
];

export function ForgeRockIdentity() {
  const [inputName, setInputName] = useState("VIN");
  const [showTable, setShowTable] = useState(false);
  const [dataLoading, setDataLoading] = useState(false);
  const [vehicleObject, setVehicleObject] = useState<VehicleResponse>();
  const { notify } = useNotification();

  const { formValues, errors, onChange, onSubmit } = useForm<IdentityI>({
    defaultValues: { inputValue: "", identifierValue: "vin" },
    validations,
  });

  useEffect(() => {
    const option = options.find((opt) => opt.value === formValues.identifierValue);
    setInputName(!option ? inputName : option.label);
  }, [inputName, formValues.identifierValue]);

  const submitValues = async ({ identifierValue, inputValue }: IdentityI) => {
    setDataLoading(true);
    setShowTable(false);
    try {
      const { data } = await get<VehicleResponse>(
        `${ENV_CONTEXT.fd_uri}/vehicle-identity/${identifierValue}/${inputValue}`
      );
      setVehicleObject(data);
    } catch (error) {
      if (error instanceof Error) {
        notify({
          type: "error",
          title: error.message,
        });
      }
    }
    setShowTable(true);
    setDataLoading(false);
  };

  const displayContent = () => {
    if (dataLoading && !showTable) return <Spinner />;

    if (showTable) {
      return (
        <Card hasBody={false} title="Returned Vehicle">
          <ul className="list-group">
            <TableItem
              title="ForgeRock Vehicle exists"
              usesIcons
              vehicleItem={vehicleObject?.frVehicleExists}
            />
            {vehicleObject?.frVehicleExists && (
              <>
                <TableItem
                  title="Digital Vehicle exists"
                  usesIcons
                  vehicleItem={vehicleObject?.dvVehicleExists}
                />
                <TableItem title="Vehicle Id" vehicleItem={vehicleObject?.["fr-vehicle-id"]} />
                <TableItem title="Client Id" vehicleItem={vehicleObject?.["fr-client-id"]} />
                <TableItem title="Public Key Type" vehicleItem={vehicleObject?.["public-key-id"]} />
                <TableItem
                  title="Passphrase matching"
                  usesIcons
                  vehicleItem={vehicleObject?.["passphrase-matching"]}
                />
                <TableItem
                  title="VIN matching"
                  usesIcons
                  vehicleItem={vehicleObject?.["vin-matching"]}
                />
                <TableItem
                  title="Vehicle Id matching"
                  usesIcons
                  vehicleItem={vehicleObject?.["vehicle-id-matching"]}
                />
                <TableItem
                  title="Client Id matching"
                  usesIcons
                  vehicleItem={vehicleObject?.["client-id-matching"]}
                />

                {vehicleObject?.users && vehicleObject.users.length > 0 && (
                  <li className="list-group-item">
                    <strong>Users: </strong>
                    {vehicleObject.users.map((user) => (
                      <li className="list-group-item" key={randomID()}>
                        {user}
                      </li>
                    ))}
                  </li>
                )}
              </>
            )}
          </ul>
        </Card>
      );
    }
  };

  return (
    <div className="container-fluid">
      <div className="row my-4">
        <div className="col-lg-5">
          <Card title="ForgeRock Identity">
            <form className="d-flex flex-column gap-3" onSubmit={onSubmit(submitValues)}>
              <TextInput
                error={errors.inputValue}
                label={`Enter ${inputName}`}
                onChange={onChange("inputValue")}
                placeholder="Type VIN here..."
                value={formValues.inputValue}
              />
              <Select
                error={errors.identifierValue}
                label="Identifier"
                onChange={onChange("identifierValue")}
                options={options}
                value={formValues.identifierValue}
              />
              <div>
                <button className="btn btn-dark my-2 noWrap" type="submit">
                  Search
                </button>
              </div>
            </form>
          </Card>
        </div>

        <div className="col-lg-7">{displayContent()}</div>
      </div>
    </div>
  );
}
