/*
 * Copyright (c) Jaguar Land Rover Ltd 2022. All rights reserved
 */

import { useState } from "react";
import { useDocTitle } from "hooks/use-doc-title.hook";
import { FilterSearch, HistoryTable } from "components/RequestHistory";
import { ConfigLogMeta } from "types/data";

export function RequestHistory() {
  const [historyData, setHistoryData] = useState<ConfigLogMeta[]>([]);
  const [loading, setLoading] = useState(false);

  useDocTitle("Request History");

  return (
    <div className="container-fluid">
      <div className="row my-4">
        <div className="col-lg-4">
          <FilterSearch setHistoryData={setHistoryData} setLoading={setLoading} />
        </div>
        <div className="col-lg-8">
          <HistoryTable historyData={historyData} loading={loading} />
        </div>
      </div>
    </div>
  );
}
