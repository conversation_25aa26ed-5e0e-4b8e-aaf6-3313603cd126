/*
 * Copyright (c) Jaguar Land Rover Ltd 2022. All rights reserved
 */

import { FormEvent, useCallback, useEffect, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { useDocTitle } from "hooks/use-doc-title.hook";
import { CsvInput, Partition, SingleInput } from "components/VinInput";
import { ControlTable, ControlToggleContainer } from "components/Control";
import { Vehicle } from "types/data";
import { useNotification } from "hooks/use-notification.hook";
import { getVehicles } from "api/digital-vehicle.api";
import { Card } from "components/Card";

export function Control() {
  useDocTitle("Control Request");

  const [vinEntered, setVinEntered] = useState("");
  const [listOfVins, setListOfVins] = useState<string[]>([]);
  const [validatedVins, setValidatedVins] = useState<string[]>([]);
  const [vehicles, setVehicles] = useState<Vehicle[]>([]);
  const [vinEnteredInCsv, setVinEnteredInCsv] = useState<string[]>([]);
  const [disableButton, setDisableButton] = useState<boolean>(true);
  const { notify } = useNotification();

  const { refetch: queryVehicles } = useQuery({
    queryKey: ["vin", listOfVins],
    queryFn: () => getVehicles({ vins: listOfVins }),
    enabled: false
  });

  const validateVin = (ev: FormEvent) => {
    ev.preventDefault();
    if (!vinEntered) {
      notify({ type: "warning", title: "Please enter a VIN" });
    } else if (listOfVins.length > 0) {
      listOfVins.forEach((existingVin) => {
        if (existingVin === vinEntered) {
          notify({ type: "warning", title: `${existingVin} already exists in list` });
          return;
        }
        setListOfVins([...listOfVins, vinEntered]);
      });
    } else {
      setListOfVins([...listOfVins, vinEntered]);
    }
  };

  const addCsvVinsToList = () => {
    setListOfVins(listOfVins.concat(vinEnteredInCsv));
  };

  const removeVin = (vinToRemove?: string) => {
    if (!vinToRemove) return;
    setListOfVins((vinList) => vinList.filter((item) => item !== vinToRemove));
    setValidatedVins((vinList) => vinList.filter((item) => item !== vinToRemove));
  };

  const searchVehicle = useCallback(
    async (clickType?: "refresh") => {
      const { data: vehicles } = await queryVehicles();

      if (vehicles?.status === 200 && vehicles?.data.data.length !== 0) {
        setValidatedVins(listOfVins);
        setVehicles(vehicles.data.data);
        if (clickType === "refresh") {
          notify({ type: "success", title: "Refreshed VIN Status" });
        }
      } else {
        notify({
          type: "error",
          title: vehicles?.data.errors ? vehicles.data.errors[0].message : "Error getting vehicles"
        });
        removeVin(listOfVins.at(-1));
      }
    },
    [queryVehicles, listOfVins, notify]
  );

  useEffect(() => {
    if (listOfVins.length === 0) {
      setVehicles([]);
    } else {
      searchVehicle();
    }
  }, [listOfVins, searchVehicle]);

  return (
    <div className="container-fluid">
      <div className="row my-4">
        <div className="col-sm-12 col-lg-5">
          <Card title="Add VINs To Toggle Controls">
            <SingleInput
              buttonText="Add"
              onVinEntered={validateVin}
              setVinEntered={setVinEntered}
            />
            <Partition />
            <CsvInput
              disableButton={disableButton}
              onVinsUploaded={addCsvVinsToList}
              setDisableButton={setDisableButton}
              setVinsEnteredAsCsv={setVinEnteredInCsv}
            />
          </Card>
          <ControlToggleContainer validatedVins={validatedVins} />
        </div>
        <div className="col-sm-12 col-lg-7">
          <ControlTable
            removeVin={removeVin}
            searchDigitalVehicle={searchVehicle}
            vehicles={vehicles}
          />
        </div>
      </div>
    </div>
  );
}
