/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

import { useState } from "react";
import { ChannelDefinitions, CreateProtocol } from "components/Obg";
import { randomID } from "utils/random";

export const Obg = () => {
  const [protocolKey, setProtocolKey] = useState(randomID());
  const resetProtocol = () => {
    setProtocolKey(randomID());
  };
  return (
    <div className="container-fluid">
      <div className="row my-4">
        <div className="col-12 col-md-4">
          <CreateProtocol key={protocolKey} resetProtocol={resetProtocol} />
        </div>
        <div className="col-12 col-md-8">
          <ChannelDefinitions />
        </div>
      </div>
    </div>
  );
};
