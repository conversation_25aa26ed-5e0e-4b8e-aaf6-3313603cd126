/*
 * Copyright (c) Jaguar Land Rover Ltd 2022. All rights reserved
 */

import { FormEvent, useContext, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { useDocTitle } from "hooks/use-doc-title.hook";
import { CsvInput, Partition, SingleInput } from "components/VinInput";
import { DataProductList, PolicyConfigs } from "components/DataProduct";
import { useNotification } from "hooks/use-notification.hook";
import { Vehicle } from "types/data";
import { VehicleContext } from "context/vehicle.context";
import { getVehicles } from "api/digital-vehicle.api";
import { Card } from "components/Card";
import { formatValidation } from "utils/format-validation";
import { InfoCircle } from "components/Icons";

export function DataProduct() {
  const [vinEntered, setVinEntered] = useState("");
  const [vinEnteredInCsv, setVinEnteredInCsv] = useState<string[]>([]);
  const [disableButton, setDisableButton] = useState<boolean>(true);

  const { vehicles, setVehicles } = useContext(VehicleContext);

  const { notify } = useNotification();

  useDocTitle("Data Product Request");

  const { refetch: queryVin } = useQuery({
    queryKey: ["vin", vinEntered],
    queryFn: () =>
      getVehicles({ vins: [vinEntered], inclusions: ["IDENTITY", "POLICIES", "APPLICATIONS", "ASSETS"] }),
    enabled: false,
  });

  const { refetch: queryCsv } = useQuery({
    queryKey: ["vin", vinEnteredInCsv],
    queryFn: () =>
      getVehicles({ vins: vinEnteredInCsv, inclusions: ["IDENTITY", "POLICIES", "APPLICATIONS", "ASSETS"] }),
    enabled: false,
  });

  const getVinsNotFound = (vinFound: string[]) => {
    const allVins = [...vinEnteredInCsv, ...vinFound];
    return allVins.filter((el) => {
      return !(vinEnteredInCsv.includes(el) && vinFound.includes(el));
    });
  };

  const getCsvConfigs = async () => {
    const vinFound: string[] = [];

    const { data: vehicleResult } = await queryCsv();

    if (vehicleResult?.status === 200) {
      const buildListOfPolicies: Vehicle[] = [];

      vehicleResult.data.data.forEach((vehicle) => {
        vinFound.push(vehicle.identity.vin);
        buildListOfPolicies.push(vehicle);
        setVehicles([...vehicles, vehicle]);
      });

      setVehicles(buildListOfPolicies);
      const vinsNotFound = getVinsNotFound(vinFound);
      if (vinsNotFound.length !== 0) {
        notify({
          type: "warning",
          title: "VINs Not Found: " + vinsNotFound.join(", "),
        });
      }
    } else {
      notify({
        type: "error",
        title: "Error occurred using the following VINs: " + vinEnteredInCsv,
      });
    }
  };

  const getVinConfig = async () => {
    const { data: vehicleResult } = await queryVin();

    if (vehicleResult?.status !== 200) {
      notify({
        type: "error",
        title: "Error occurred querying " + vinEntered,
      });
      return;
    }

    if (vehicleResult?.data?.data.length === 0) {
      notify({
        type: "error",
        title: "Vehicle not found for " + vinEntered,
      });
      return;
    }

    vehicleResult.data.data.forEach((vehicle) => {
      setVehicles([...vehicles, vehicle]);
    });
  };

  const singleVinValidation = (ev: FormEvent) => {
    ev.preventDefault();
    if (!vinEntered) {
      notify({ type: "warning", title: "Please Enter a VIN" });
      return;
    }
    const { formattedVins } = formatValidation([vinEntered]);
    if (formattedVins[0]) {
      const noDuplicates = vehicles.some((vehicle) => vinEntered.includes(vehicle.identity.vin));
      if (!noDuplicates) {
        getVinConfig();
      } else {
        notify({ type: "warning", title: "VIN already added to list" });
      }
    } else {
      notify({ type: "warning", title: `${vinEntered} is not a valid VIN` });
    }
  };

  return (
    <div className="container-fluid">
      <div className="alert alert-primary my-4">
        <div className="d-flex justify-content-start gap-3 align-items-center mb-3">
          <InfoCircle size="27" />
          <h4 className="m-0">
            Data Products can only be manually applied to vehicles under the following conditions:
          </h4>
        </div>
        <ul>
          <li>
            The vehicle's <em>Automation Status</em> must be <u>Non-Auto</u>
          </li>
          <li>
            The vehicle must have at least one of the <em>Permissions</em> defined in the Data
            Product
          </li>
          <li>
            The Data Product will only be applied to vehicles that match the Data Product's
            <em> Eligibility Criteria</em>
          </li>
        </ul>
        <p>
          Click <em>Inspect</em> on a Data Product to review it's Permissions and Eligibility
          Criteria.
        </p>
      </div>

      <div className="row my-4">
        <div className="col-lg-6">
          <Card title="Add VINs To Apply Data Products">
            <SingleInput
              buttonText="Add"
              onVinEntered={singleVinValidation}
              setVinEntered={setVinEntered}
            />
            <Partition />
            <CsvInput
              disableButton={disableButton}
              onVinsUploaded={getCsvConfigs}
              setDisableButton={setDisableButton}
              setVinsEnteredAsCsv={setVinEnteredInCsv}
            />
          </Card>
          <PolicyConfigs />
        </div>
        <div className="col-lg-6">
          <DataProductList />
        </div>
      </div>
    </div>
  );
}
