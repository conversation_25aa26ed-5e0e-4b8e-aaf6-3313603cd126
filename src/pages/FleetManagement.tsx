/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

import { useMutation, useQuery } from "@tanstack/react-query";
import { useContext, useState } from "react";
import { chunk } from "lodash";
import { CsvInput, Partition, TwoButtonInput } from "components/VinInput";
import { FleetCounter, FleetUpdate, VinFeedback } from "components/FleetManagement";
import { VehicleIdentity } from "types/fleet-management";
import { useNotification } from "hooks/use-notification.hook";
import { FleetContext } from "context/fleet.context";
import { Card } from "components/Card";
import { getVehicles } from "api/digital-vehicle.api";
import { searchVehiclesByFleetId } from "api/fleet-managment.api";

export const FleetManagement = () => {
  const { notify } = useNotification();
  const [successState, setSuccessState] = useState(false);
  const [csvVins, setCsvVins] = useState<string[]>([]);
  const [showValidationScreen, setShowValidationScreen] = useState(false);
  const [disableButton, setDisableButton] = useState<boolean>(true);
  const [vinEntered, setVinEntered] = useState("");

  const {
    duplicateVins,
    invalidVins,
    vinsToAdd,
    vinsToRemove,
    setDuplicateVins,
    setInvalidVins,
    setVinsToAdd,
    setVinsToRemove,
  } = useContext(FleetContext);

  const {
    refetch: searchFleetVehicles,
    isRefetching: searchFleetLoading,
    data: fleetVehicles,
  } = useQuery({
    queryKey: ["FleetSearch"],
    queryFn: () => searchVehiclesByFleetId("MLAP"),
  });

  const { refetch: fetchVehicle } = useQuery({
    queryKey: ["Vehicle", vinEntered],
    queryFn: () => getVehicles({ vins: [vinEntered], inclusions: ["IDENTITY"] }),
    enabled: false,
  });

  const fetchAllVehicles = useMutation({ mutationFn: getVehicles });

  const resetVins = () => {
    setShowValidationScreen(false);
    setVinsToAdd([]);
    setVinsToRemove([]);
    setInvalidVins([]);
    setDuplicateVins([]);
    setVinEntered("");
    setDisableButton(false);
  };

  const handleNewInvalidVins = (vins: string[]) => {
    resetVins();
    setInvalidVins(vins);
  };

  const validateVins = async () => {
    const searchDvBatch = chunk(csvVins, 1000);
    const responses = await Promise.all(
      searchDvBatch.map((vins) =>
        fetchAllVehicles.mutateAsync({
          vins,
          inclusions: ["IDENTITY"],
        })
      )
    );
    const dvVehiclesData = responses.flatMap((response) => response.data.data);
    const validVinsFromCsv = dvVehiclesData.map((vehicle) => vehicle.identity.vin) ?? [];
    const differences = csvVins.filter((vin) => !validVinsFromCsv.includes(vin));
    setInvalidVins([...invalidVins, ...differences]);
    setShowValidationScreen(true);
    if (validVinsFromCsv.length > 0) {
      searchVehicles(validVinsFromCsv, "csv");
    } else {
      return;
    }
  };

  const singleVinAdded = async (path: "add" | "remove") => {
    const { data: vehicle } = await fetchVehicle();
    const validVin = vehicle?.data?.data[0]?.identity?.vin;
    if (!validVin) {
      notify({
        type: "error",
        title: "VIN does not exist",
      });
    }

    if (vinEntered !== validVin) {
      resetVins();
    } else {
      searchVehicles([validVin], path);
    }
  };

  const compareVinLists = (vehicleData: VehicleIdentity[], validatedVins: string[]) => {
    const dvVins = vehicleData.map((vehicle) => vehicle?.identity.vin);
    const duplicates: string[] = [];
    const addVins: string[] = [];
    for (const vin of validatedVins) {
      if (dvVins.includes(vin)) {
        duplicates.push(vin);
      } else {
        addVins.push(vin);
      }
    }
    const removeVins = dvVins.filter((vin) => !validatedVins?.includes(vin));
    if (addVins.length === 0 && removeVins.length === 0) {
      notify({
        type: "warning",
        title: "Vehicles fleet ID already up to date with CSV uploaded",
        body: "Please upload a new CSV",
      });
    } else {
      setDuplicateVins([...duplicateVins, ...duplicates]);
      setVinsToAdd([...vinsToAdd, ...addVins]);
      setVinsToRemove([...vinsToRemove, ...removeVins]);
    }
  };

  const removeVinFromFleet = (vehicleData: VehicleIdentity[], validVin: string[]) => {
    const dvVins = vehicleData.map((vehicle) => vehicle?.identity.vin);
    if (!dvVins.includes(validVin[0])) {
      notify({
        type: "warning",
        title: "Vehicle does not exist in MLAP fleet",
      });
      return;
    }
    if (vinsToRemove.includes(validVin[0])) {
      notify({
        type: "warning",
        title: `${validVin[0]} already entered`,
      });
      return;
    }
    setVinsToRemove([...vinsToRemove, ...validVin]);
    setVinEntered("");
  };

  const addVinToFleet = (vehicleData: VehicleIdentity[], validVin: string[]) => {
    const dvVins = vehicleData.map((vehicle) => vehicle?.identity.vin);
    if (dvVins.includes(validVin[0])) {
      notify({
        type: "warning",
        title: "Vehicle already present in MLAP fleet",
      });
      return;
    }
    if (vinsToAdd.includes(validVin[0])) {
      notify({
        type: "warning",
        title: `VIN, ${validVin[0]}, already entered`,
      });
      return;
    }
    setVinsToAdd([...vinsToAdd, ...validVin]);
    setVinEntered("");
  };

  const searchVehicles = async (validVins: string[], path: "csv" | "add" | "remove") => {
    setSuccessState(false);
    const { data: vehicleData, isSuccess } = await searchFleetVehicles();
    if (isSuccess) {
      switch (path) {
        case "csv":
          compareVinLists(vehicleData, validVins);
          break;
        case "add":
          addVinToFleet(vehicleData, validVins);
          break;
        case "remove":
          removeVinFromFleet(vehicleData, validVins);
          break;
        default:
          break;
      }
    }
  };

  return (
    <div className="container-fluid">
      <div className="row my-4">
        <div className="col-12 col-lg-4">
          <Card title="Update MLAP Fleet">
            <TwoButtonInput
              onVinAdded={() => singleVinAdded("add")}
              onVinRemoved={() => singleVinAdded("remove")}
              setVinEntered={setVinEntered}
              vinEntered={vinEntered}
            />
            <Partition />
            <CsvInput
              disableButton={disableButton}
              onVinsUploaded={validateVins}
              setDisableButton={setDisableButton}
              setInvalidVinsFromCsv={handleNewInvalidVins}
              setVinsEnteredAsCsv={setCsvVins}
            />
          </Card>
          <VinFeedback visible={showValidationScreen} />
        </div>
        <div className="col-12 col-lg-5">
          <FleetUpdate
            refetchFleetVehicles={searchFleetVehicles}
            resetVins={resetVins}
            searchFleetLoading={searchFleetLoading}
            setSuccessState={setSuccessState}
            successState={successState}
          />
        </div>
        <div className="col-12 col-lg-3">
          <FleetCounter fleetVehicles={fleetVehicles} />
        </div>
      </div>
    </div>
  );
};
