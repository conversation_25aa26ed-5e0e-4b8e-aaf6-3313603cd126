/*
 * Copyright (c) Jaguar Land Rover Ltd 2022. All rights reserved
 */

type ErrorProps = {
  error: { message: string };
};

import { useState } from "react";
import { Navbar } from "components/App/Navbar";
import { Sidebar } from "components/App/Sidebar";
import { ErrorPage } from "components/Errors";

// Internal Server 500 Error
export const InternalServerError = () => {
  return <ErrorPage error="INTERNAL SERVER ERROR." />;
};

// Page Not Found Error
export const NotFoundError = () => {
  return <ErrorPage error="PAGE NOT FOUND." />;
};

export const ErrorFallback = ({ error }: ErrorProps) => {
  const [sidebar, setSidebar] = useState(true);
  const showSidebar = () => {
    setSidebar(!sidebar);
  };
  return (
    <>
      <Navbar showSidebar={showSidebar} sidebar={sidebar} />
      <div id="layout">
        <Sidebar sidebar={sidebar} />
        <ErrorPage error={error.message} />
      </div>
    </>
  );
};
