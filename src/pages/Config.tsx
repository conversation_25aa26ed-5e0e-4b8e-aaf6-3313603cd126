/*
 * Copyright (c) Jaguar Land Rover Ltd 2022. All rights reserved
 */

import { FormEvent, useCallback, useContext, useEffect, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { ENV_CONTEXT } from "utils/constants";
import { useDocTitle } from "hooks/use-doc-title.hook";
import { get } from "services/fetch.service";
import { AuthContext } from "context/auth.context";
import { SingleInput } from "components/VinInput";
import { ConfigList, CreateConfig, ManifestData } from "components/Config";
import { useNotification } from "hooks/use-notification.hook";
import {
  defaultVehicleConfig,
  DisableOptionsProps,
  Vehicle,
  VehicleConfig,
  VehicleContent,
} from "types/data";
import { getVehicles } from "api/digital-vehicle.api";
import { Card } from "components/Card";

export function Config() {
  const { roles } = useContext(AuthContext);
  const [vinEntered, setVinEntered] = useState("");
  const { notify } = useNotification();

  // Set these values when auth is in place to allow users to create these configs
  const [disableOptions, setDisableOptions] = useState<DisableOptionsProps>({
    disableSignal: true,
    disableDiagnostic: true,
    disableChannel: true,
  });
  const [disableUserInput, setDisableUserInput] = useState(false);

  const [dvData, setDvData] = useState<Vehicle>();
  const [configFiles, setConfigFiles] = useState<VehicleConfig[]>(defaultVehicleConfig);
  const [loadingConfigs, setLoadingConfigs] = useState(true);

  useDocTitle("Config Request");

  const { refetch: queryVehicles } = useQuery({
    queryKey: ["vin", vinEntered],
    queryFn: () => getVehicles({ vins: [vinEntered] }),
    enabled: false,
  });

  const getConfigFiles = useCallback(async () => {
    setLoadingConfigs(true);
    try {
      const { data: meta, status } = await get<VehicleContent>(
        `${ENV_CONTEXT.cc_uri}/api/v1/config/getmeta`
      );
      if (status === 200) {
        setConfigFiles(meta.data);
      } else {
        notify({
          type: "error",
          title: "Error getting meta files",
        });
      }
    } catch (error) {
      if (error instanceof Error) {
        notify({
          type: "error",
          title: error.message,
        });
      }
    }
  }, [notify]);

  useEffect(() => {
    getConfigFiles();
    if (roles.some((role) => ["admin", "tester"].includes(role))) {
      setDisableOptions((prevValues) => ({
        ...prevValues,
        disableChannel: false,
        disableDiagnostic: false,
        disableSignal: false,
      }));
    }
  }, [roles, getConfigFiles]);

  const searchVehicle = async (ev: FormEvent) => {
    ev.preventDefault();

    if (!vinEntered) {
      notify({ type: "warning", title: "Please enter a VIN" });
      return;
    }
    const { data: vehicles } = await queryVehicles();

    if (vehicles?.status !== 200) {
      notify({
        type: "error",
        title: vehicles?.data.errors ? vehicles.data.errors[0].message : "Error getting vehicle",
      });
      return;
    }

    setDvData(vehicles.data.data[0]);
  };

  return (
    <div className="container-fluid">
      <div className="row my-4">
        <div className="col-sm-12 col-lg-6 col-xl-3">
          <Card title="Get VIN Manifest and Feedback">
            <SingleInput onVinEntered={searchVehicle} setVinEntered={setVinEntered} />
          </Card>
          <ManifestData
            errors={dvData?.applications?.vehicleAnalytics?.errorMessages}
            manifest={dvData?.applications?.vehicleAnalytics?.manifest}
            obg={dvData?.applications?.obg?.isEnabled}
            vehicleArchitecture={dvData?.assets.vehicleArchitecture}
            vehicleStatus={
              dvData?.applications?.vehicleAnalytics?.manifest?.vaConfigAcceptErrorState
            }
            vin={dvData?.identity?.vin}
          />
        </div>
        <div className="col-sm-12 col-lg-6 col-xl-4">
          <CreateConfig
            disableOptions={disableOptions}
            disableUserInput={disableUserInput}
            getConfigFiles={getConfigFiles}
            setDisableUserInput={setDisableUserInput}
          />
        </div>
        <div className="col-sm-12 col-lg-12 col-xl-5">
          <ConfigList
            configFiles={configFiles}
            loadingConfigs={loadingConfigs}
            setLoadingConfigs={setLoadingConfigs}
            updateConfigs={getConfigFiles}
            vinEntered={vinEntered}
          />
        </div>
      </div>
    </div>
  );
}
