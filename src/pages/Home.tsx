/*
 * Copyright (c) Jaguar Land Rover Ltd 2022. All rights reserved
 */

import { useContext } from "react";
import { useDocTitle } from "hooks/use-doc-title.hook";
import { AuthContext } from "context/auth.context";

export function Home() {
  const { firstName, lastName } = useContext(AuthContext);
  useDocTitle("Config Center");

  return (
    <div className="container-fluid">
      <div className="row m-4">
        <h1 className="display-4">
          Welcome {firstName} {lastName}
        </h1>
        <hr className="my-4" />
        <p>
          The Config Center GUI is a way to manage config requests, control requests, data product
          requests, view digital vehicle information, and view the history of requests. It
          consolidates Config Centre subsystem functionality into a demo-able interface.
        </p>
        <p>
          Please choose an option from the sidebar menu to the left. Check out the{" "}
          <a
            href="https://confluence.devops.jlr-apps.com/display/VCDP/Config+Center+GUI"
            rel="noopener noreferrer"
            target="_blank"
          >
            Config Center Guide
          </a>{" "}
          on Confluence for more information.
        </p>
        <h2 className="my-3">Overview</h2>
        <b className="m-0">Config Request Page</b>
        <p>
          Allows users to apply Configuration files to specific Vehicles (VIN), create, view, update
          and delete Configuration files.
        </p>
        <b className="m-0">Control Request Page</b>
        <p>Allows users to update VA command controls for multiple VINs at a time.</p>
        <b className="m-0">Data Product Page</b>
        <p>Allows users to apply or remove Data Products on one or more vehicles (VINs).</p>
        <b className="m-0">Digital Vehicle Page</b>
        <p>
          Displays the Digital Vehicle representation of your selected VIN. It includes manifest and
          control status details of the VIN.
        </p>
        <b className="m-0">Request History Page</b>
        <p>
          Allows for audit functionality to see historical logs and what has been pushed through to
          vehicles previously.
        </p>
        <b className="m-0">Forgerock Identity Page</b>
        <p>
          Allows users to query vehicle identity information using VIN, vehicleId or Forgerock
          clientId. This information is collected from numerous sources, collated and displayed.
        </p>
        <b className="m-0">Fleet Management Page</b>
        <p>
          Allows users to manage what vehicles are part of the MLAP fleet. Upload a CSV of VINs to
          to add and remove vehicles from the MLAP fleet.
        </p>
      </div>
    </div>
  );
}
