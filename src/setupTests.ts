/*
 * Copyright (c) Jaguar Land Rover Ltd 2022. All rights reserved
 */

// jest-dom adds custom jest matchers for asserting on DOM nodes.
// allows you to do things like:
// expect(element).toHaveTextContent(/react/i)
// learn more: https://github.com/testing-library/jest-dom

import "@testing-library/jest-dom";
import "whatwg-fetch";
import { afterAll, afterEach, beforeAll, vi } from "vitest";
import { server } from "msw/server";

// Establish API mocking before all tests.
beforeAll(() => server.listen());

// Reset any request handlers that we may add during the tests,
// so they don't affect other tests.
afterEach(() => server.resetHandlers());

// Clean up after the tests are finished.
afterAll(() => server.close());

vi.mock("react-query", () => ({
  useQueryClient: vi.fn().mockReturnValue({ data: "mocked response", isLoading: false, error: {} }),
}));

vi.mock("keycloak-js", () => {
  const Keycloak = vi.fn();
  Keycloak.prototype.authenticated = false;
  Keycloak.prototype.token = "token available";
  Keycloak.prototype.tokenParsed = {
    given_name: "dummy",
    family_name: "user",
    preferred_username: "dummy user",
    authorities: ["admin"],
  };

  Keycloak.prototype.login = vi.fn();
  Keycloak.prototype.logout = vi.fn();

  return { default: Keycloak };
});

window.matchMedia = (query) => ({
  addEventListener: vi.fn(),
  addListener: vi.fn(),
  dispatchEvent: vi.fn(),
  matches: false,
  media: query,
  onchange: null,
  removeEventListener: vi.fn(),
  removeListener: vi.fn(),
});
