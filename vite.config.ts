/*
 * Copyright 2022 (c) Jaguar Land Rover Ltd. All rights reserved.
 */

/* eslint-disable import/no-default-export */
/// <reference types="vitest" />

import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import tsconfigPaths from "vite-tsconfig-paths";

/**
 * node_modules is mostly the main reason for the large chunk problem,
 * With this you're telling Vite to treat the used modules separately.
 */

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react(), tsconfigPaths()],
  build: {
    outDir: "build",
    rollupOptions: {
      output: {
        manualChunks(id) {
          if (id.includes("node_modules")) {
            return id.toString().split("node_modules/")[1].split("/")[0].toString();
          }
        },
      },
    },
  },
  server: { port: 3000 },
  // Testing using https://vitest.dev/guide/
  test: {
    setupFiles: "./src/setupTests.ts",
    globals: true,
    environment: "jsdom",
    testTimeout: 10000,
    coverage: {
      provider: "v8",
      reporter: ["text", "json", "html", "lcov"],
    },
  },
});
