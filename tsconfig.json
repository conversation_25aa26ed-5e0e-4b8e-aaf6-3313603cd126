{
  "compilerOptions": {
    /* Visit https://aka.ms/tsconfig to read more about this file */

    /* Language and Environment */
    "target": "ESNext" /* Set the JavaScript language version for emitted JavaScript and include compatible library declarations. */,
    "lib": [
      "DOM",
      "DOM.Iterable",
      "ESNext"
    ] /* Specify a set of bundled library declaration files that describe the target runtime environment. */,
    "jsx": "react-jsx" /* Specify what JSX code is generated. */,

    /* Modules */
    "module": "ESNext" /* Specify what module code is generated. */,
    "moduleResolution": "Node",
    "baseUrl": "src" /* Specify the base directory to resolve non-relative module names. */,
    "resolveJsonModule": true /* Enable importing .json files. */,

    /* JavaScript Support */
    "allowJs": false /* Allow JavaScript files to be a part of your program. Use the 'checkJS' option to get errors from these files. */,

    /* Emit */
    "sourceMap": true /* Create source map files for emitted JavaScript files. */,
    "outDir": "./build" /* Specify an output folder for all emitted files. */,
    "noEmit": true /* Disable emitting files from a compilation. */,

    /* Interop Constraints */
    "isolatedModules": true /* Ensure that each file can be safely transpiled without relying on other imports. */,
    "allowSyntheticDefaultImports": true /* Allow 'import x from y' when a module doesn't have a default export. */,
    "esModuleInterop": true /* Emit additional JavaScript to ease support for importing CommonJS modules. This enables 'allowSyntheticDefaultImports' for type compatibility. */,
    "forceConsistentCasingInFileNames": true /* Ensure that casing is correct in imports. */,

    /* Type Checking */
    "strict": true /* Enable all strict type-checking options. */,
    "noImplicitAny": true /* Enable error reporting for expressions and declarations with an implied 'any' type. */,

    "skipLibCheck": true /* Skip type checking all .d.ts files. */,
    "types": ["vite/client"] /* Types for the Vite-injected env variables on import.meta.env */
  }
}
