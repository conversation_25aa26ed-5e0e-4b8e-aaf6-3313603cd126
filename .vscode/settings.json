{"eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "editor.tabSize": 2, "editor.detectIndentation": false, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "eslint.enable": true, "eslint.format.enable": true, "prettier.printWidth": 100, "typescript.updateImportsOnFileMove.enabled": "always", "javascript.updateImportsOnFileMove.enabled": "always"}