image: node:18.20.3-alpine3.20

include:
  - project: "d9/infrastructure/gitlab-ci-templates"
    ref: master
    file: ".k8s-nodejs-gitlab-ci-template.yml"
  - project: "D9/infrastructure/gitlab-ci-templates"
    ref: master
    file: ".k8s-nodejs-gitlab-ci-template-cn.patch.yml"
    inputs:
      deploy_aws_dev_cn: true
      deploy_aws_preprod_cn: true
      deploy_aws_prod_cn: true

install:
  stage: install
  script:
    - npm ci
  artifacts:
    expire_in: 1 days
    when: on_success
    paths:
      - node_modules/

audit:
  stage: validate
  script:
    - npm audit --production

build:
  image: node:18.20.3-alpine3.20
  cache:
    key: $CI_COMMIT_REF_SLUG-$CI_PROJECT_DIR
    paths:
      - node_modules/
      - "**/node_modules/"
    policy: pull

lint:
  stage: validate
  image: node:18.20.3-alpine3.20
  dependencies:
    - install
  script:
    - npm run lint

unittests:
  stage: validate
  image: node:18.20.3-alpine3.20
  dependencies:
    - install
  script:
    - npm run test.coverage
  artifacts:
    paths:
      - coverage
      - coverage/lcov.info
    when: always
  coverage: '/^All files\s+\|\s+(\d+\.*\d*)\s+\|/'
