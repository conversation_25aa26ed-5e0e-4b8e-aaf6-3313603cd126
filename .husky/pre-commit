#!/usr/bin/env sh
#
# Copyright (c) Jaguar Land Rover Ltd 2022. All rights reserved
#

. "$(dirname -- "$0")/_/husky.sh"

echo "👷 🔨🔨 Running lint tests before committing code."

npm run lint || (
    echo "❌ ESLint Check Failed. Make the required changes listed above, add changes and try to commit again."
    false;
)

echo "code linting fine, auditing production code."

npm audit --production || (
    echo "❌ Audit Failed. Fix errors and try again."
    false;
)

npm run test || (
    echo "❌ Tests: view the errors above and fix."
    false;
)

echo "✅✅✅ Commit passed ✅✅✅"
