# VLC Config Center GUI

## Overview

The Config Center GUI is a way to manage Config Requests, Control Requests, Data Product Requests, view Digital Vehicles, and view Request Histories. It consolidates Config Center subsystem functionality into a demo-able interface.

View more on Confluence about using the GUI: https://confluence.devops.jlr-apps.com/display/VCDP/Config+Center+GUI#

## Start the application locally

### `Code Editor`

Choosing VS Code as your code editor is recommended as the following extensions are great for linting and code style: [Prettier](https://marketplace.visualstudio.com/items?itemName=esbenp.prettier-vscode) and [ESLint](https://marketplace.visualstudio.com/items?itemName=dbaeumer.vscode-eslint).

If you are using Intellij, [Prettier](https://www.jetbrains.com/help/idea/prettier.html) will be only available on the Ultimate edition. The following changes are recommended in settings --> Editor --> Code Style --> JavaScript:

- Tabs and Indents - set Tab Size, Indent, and Continuous Indent to 2.
- Spaces - Turn on ES6 import/export braces

### `AWS Dev VPN`

You must be connected to the Dev VPN. Learn more about setting it up here if you haven't already: https://confluence.devops.jlr-apps.com/display/VCDP/AWS+VPN+Setup

### `Node.js`

Download [Node.js](https://nodejs.org/en) if you haven't already. It is a JavaScript runtime environment that React runs on.

**Note: Currently using node v20.0.0 or higher for development.**

### `npm i`

After installing Node.js, run this command to install the project dependencies. If there any issues, try to delete the package-lock.json and node_modules folder and try again.

### `npm start`

Runs the app in the development mode on http://localhost:3000 in the browser. Reminder that you must be on the AWS VPN to login otherwise you will only see a blank screen.

## Troubleshooting

Use the Confluence page for updated information about troubleshooting issues: https://confluence.devops.jlr-apps.com/display/VCDP/Config+Center+GUI#

## Development Scripts

### `npm test`

Launches the test runner.

### `npm run lint` / `npm run lint.fix`

You can manually run the linting with npm run lint and it will show any errors in the terminal.\
Run npm lint.fix to try fix any current errors.

## License and copyright

Copyright (c) 2024. Jaguar Land Rover - All Rights Reserved.\
CONFIDENTIAL INFORMATION - DO NOT DISTRIBUTE
