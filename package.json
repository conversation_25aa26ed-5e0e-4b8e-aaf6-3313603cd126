{"name": "config-center-gui", "version": "0.1.0", "private": true, "type": "module", "engines": {"node": ">18.0.0"}, "dependencies": {"@datadog/browser-rum": "^4.42.2", "@floating-ui/react-dom": "^2.0.4", "@popperjs/core": "^2.11.8", "@tanstack/react-query": "^5.8.4", "bootstrap": "^5.3.2", "keycloak-js": "^22.0.4", "lodash": "^4.17.21", "react": "^18.2.0", "react-dom": "^18.2.0", "react-error-boundary": "^3.1.4", "react-json-pretty": "^2.2.0", "react-router-dom": "^6.6.1", "react-transition-group": "^4.4.5", "web-vitals": "^3.1.0"}, "scripts": {"start": "vite", "build": "tsc && vite build", "test": "vitest run --silent", "test.coverage": "vitest run --coverage --silent", "lint": "tsc --noEmit && eslint --cache .", "lint.fix": "tsc --noEmit && eslint --cache --fix .", "prepare": "husky install"}, "browserslist": ["last 2 versions", "> 0.2%", "not dead"], "devDependencies": {"@eslint/compat": "^1.2.9", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.26.0", "@testing-library/dom": "^8.19.1", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.3", "@types/bootstrap": "^5.2.6", "@types/lodash": "^4.14.200", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@types/react-transition-group": "^4.4.11", "@typescript-eslint/eslint-plugin": "^8.32.0", "@typescript-eslint/parser": "^8.32.0", "@vitejs/plugin-react": "^4.2.0", "@vitest/coverage-v8": "^2.1.8", "eslint": "^9.4.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-react": "^7.34.2", "eslint-plugin-react-hooks": "^5.2.0", "globals": "^15.15.0", "husky": "^8.0.3", "jsdom": "^24.0.0", "msw": "^2.2.0", "sass": "^1.77.4", "typescript": "^5.2.2", "vite": "^5.1.7", "vite-tsconfig-paths": "^4.2.1", "vitest": "^2.1.8", "whatwg-fetch": "^3.6.17"}, "overrides": {"braces": "^3.0.3", "micromatch": "^4.0.6"}}