# build environment
FROM node:18.20.3-alpine3.20 as react-build

RUN apk upgrade --available

# create a new non-root USER
RUN addgroup -S new-group && adduser -S new-user -G new-group
USER new-user
WORKDIR /app
# Copy the React App to the container
COPY . /app/
# Prepare the container for building React
RUN npm install
# Want the production version
RUN npm run build
# server environment
FROM nginx:alpine
RUN apk upgrade --available
COPY nginx.conf /etc/nginx/conf.d/configfile.template
COPY --from=react-build /app/build /usr/share/nginx/html
ENV PORT 8080
ENV HOST 0.0.0.0
# Fire up nginx
EXPOSE 8080
CMD sh -c "envsubst '\$PORT' < /etc/nginx/conf.d/configfile.template > /etc/nginx/conf.d/default.conf && nginx -g 'daemon off;'"
